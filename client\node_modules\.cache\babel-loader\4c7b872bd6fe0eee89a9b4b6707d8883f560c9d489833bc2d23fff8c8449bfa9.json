{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\bimbo-hunter-base\\\\client\\\\src\\\\components\\\\Board\\\\PortraitOverlay.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PortraitOverlay = ({\n  character,\n  onClose,\n  onClaim,\n  sourcePosition,\n  isClaimed,\n  isReadOnly,\n  userId,\n  boardId,\n  squareIndex,\n  userImage\n}) => {\n  _s();\n  // Get the portrait URL from the portrait path\n  const getPortraitUrl = portraitPath => {\n    if (!portraitPath) return null;\n    return `${process.env.PUBLIC_URL}${portraitPath}`;\n  };\n\n  // Use user image if available and claimed, otherwise use original portrait\n  const portraitUrl = isClaimed && userImage ? `${process.env.PUBLIC_URL}${userImage}` : getPortraitUrl(character.Portrait);\n\n  // Get the frame overlay URL based on character rarity\n  const getFrameOverlayUrl = rarity => {\n    if (!rarity || rarity === 'FREE') return null; // No frame for FREE characters\n    return `${process.env.PUBLIC_URL}/frames/${rarity} - Portrait.png`;\n  };\n  const frameOverlayUrl = getFrameOverlayUrl(character.rarity);\n\n  // Use state to control animation classes and details visibility\n  const [isVisible, setIsVisible] = useState(false);\n  const [showDetails, setShowDetails] = useState(false);\n  const [selectedFile, setSelectedFile] = useState(null);\n  const [uploading, setUploading] = useState(false);\n  const [showUpload, setShowUpload] = useState(false);\n  const fileInputRef = useRef(null);\n\n  // Apply the animation after component mounts\n  useEffect(() => {\n    // Small delay to ensure the component is rendered before animation starts\n    const timer = setTimeout(() => {\n      setIsVisible(true);\n    }, 50);\n    return () => clearTimeout(timer);\n  }, []);\n\n  // Handle closing with animation\n  const handleClose = () => {\n    setIsVisible(false);\n    setShowDetails(false);\n    // Wait for animation to complete before actually closing\n    setTimeout(onClose, 300);\n  };\n\n  // Handle file selection\n  const handleFileSelect = event => {\n    const file = event.target.files[0];\n    if (file) {\n      setSelectedFile(file);\n      setShowUpload(true);\n    }\n  };\n\n  // Handle file upload\n  const handleUpload = async () => {\n    if (!selectedFile || !userId || !boardId || squareIndex === undefined) return;\n    setUploading(true);\n    const formData = new FormData();\n    formData.append('file', selectedFile);\n    try {\n      const response = await fetch(`http://localhost:5000/api/users/${userId}/boards/${boardId}/upload/${squareIndex}`, {\n        method: 'POST',\n        body: formData\n      });\n      if (response.ok) {\n        const result = await response.json();\n        // Call onClaim with the uploaded image path\n        onClaim(result.image_path);\n        setIsVisible(false);\n        setShowDetails(false);\n        setShowUpload(false);\n      } else {\n        console.error('Upload failed');\n        alert('Failed to upload image. Please try again.');\n      }\n    } catch (error) {\n      console.error('Upload error:', error);\n      alert('Failed to upload image. Please try again.');\n    } finally {\n      setUploading(false);\n    }\n  };\n\n  // Handle claiming with animation (for unclaiming)\n  const handleClaim = () => {\n    if (isReadOnly) return; // Disable claiming for read-only mode\n\n    if (isClaimed) {\n      // If already claimed, just unclaim\n      setIsVisible(false);\n      setShowDetails(false);\n      setTimeout(() => onClaim(null), 300);\n    } else {\n      // If not claimed, show upload interface\n      setShowUpload(true);\n    }\n  };\n\n  // Handle showing/hiding details\n  const handleToggleDetails = () => {\n    setShowDetails(!showDetails);\n  };\n\n  // Get rarity value text\n  const getRarityValue = rarity => {\n    switch (rarity) {\n      case 'FREE':\n        return 'FREE (1 pt)';\n      case 'R':\n        return 'R (2 pts)';\n      case 'SR':\n        return 'SR (3 pts)';\n      case 'SSR':\n        return 'SSR (4 pts)';\n      case 'UR+':\n        return 'UR+ (6 pts)';\n      default:\n        return 'Unknown';\n    }\n  };\n\n  // Get rarity color for details button\n  const getRarityColor = rarity => {\n    switch (rarity) {\n      case 'FREE':\n        return '#4CAF50';\n      // Green\n      case 'R':\n        return '#2196F3';\n      // Blue\n      case 'SR':\n        return '#9C27B0';\n      // Purple\n      case 'SSR':\n        return '#FF9800';\n      // Orange\n      case 'UR+':\n        return '#F44336';\n      // Red\n      default:\n        return '#2196F3';\n      // Default blue\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `portrait-overlay ${isVisible ? 'visible' : ''}`,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `portrait-container ${isVisible ? 'visible' : ''}`,\n      style: sourcePosition ? {\n        // If we have source position, use it for initial transform origin\n        transformOrigin: `${sourcePosition.x}px ${sourcePosition.y}px`\n      } : {},\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"close-button\",\n        onClick: handleClose,\n        children: \"\\xD7\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 156,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"portrait-image-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"img\", {\n          src: portraitUrl,\n          alt: character.Name,\n          className: \"character-portrait\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 11\n        }, this), frameOverlayUrl && /*#__PURE__*/_jsxDEV(\"img\", {\n          src: frameOverlayUrl,\n          alt: `${character.rarity} frame`,\n          className: \"portrait-frame-overlay\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 164,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 157,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"portrait-buttons\",\n        children: [!isReadOnly && !showUpload && /*#__PURE__*/_jsxDEV(\"button\", {\n          className: `claim-button ${isClaimed ? 'unclaim' : ''}`,\n          onClick: handleClaim,\n          children: isClaimed ? 'Unclaim' : 'Claim!'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 173,\n          columnNumber: 13\n        }, this), !showUpload && /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"details-button\",\n          onClick: handleToggleDetails,\n          style: {\n            backgroundColor: getRarityColor(character.rarity),\n            boxShadow: `0 0 20px ${getRarityColor(character.rarity)}80, 0 4px 6px rgba(0, 0, 0, 0.1)`\n          },\n          children: \"Details\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 171,\n        columnNumber: 9\n      }, this), showUpload && !isReadOnly && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"character-details-overlay\",\n        onClick: () => {\n          setShowUpload(false);\n          setSelectedFile(null);\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"character-details-content upload-overlay-content\",\n          onClick: e => e.stopPropagation() // Prevent clicks on content from closing\n          ,\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"upload-title\",\n            children: \"Upload Your Photo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"upload-description\",\n            children: \"Upload a photo of yourself with this cosplayer to claim this square!\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 208,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"file\",\n            ref: fileInputRef,\n            onChange: handleFileSelect,\n            accept: \"image/*\",\n            style: {\n              display: 'none'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 210,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"upload-buttons\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"details-button upload-select-button\",\n              onClick: () => {\n                var _fileInputRef$current;\n                return (_fileInputRef$current = fileInputRef.current) === null || _fileInputRef$current === void 0 ? void 0 : _fileInputRef$current.click();\n              },\n              disabled: uploading,\n              style: {\n                backgroundColor: getRarityColor(character.rarity),\n                boxShadow: `0 0 20px ${getRarityColor(character.rarity)}80, 0 4px 6px rgba(0, 0, 0, 0.1)`\n              },\n              children: selectedFile ? 'Change Photo' : 'Select Photo'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 17\n            }, this), selectedFile && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"selected-file-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  style: {\n                    color: getRarityColor(character.rarity)\n                  },\n                  children: \"Selected:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 233,\n                  columnNumber: 24\n                }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 233,\n                  columnNumber: 102\n                }, this), selectedFile.name]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 233,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"upload-actions\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"details-button upload-confirm-button\",\n                  onClick: handleUpload,\n                  disabled: uploading,\n                  style: {\n                    backgroundColor: '#4CAF50',\n                    boxShadow: '0 0 20px #4CAF5080, 0 4px 6px rgba(0, 0, 0, 0.1)'\n                  },\n                  children: uploading ? 'Uploading...' : 'Upload & Claim'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 235,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"details-button upload-cancel-button\",\n                  onClick: () => {\n                    setShowUpload(false);\n                    setSelectedFile(null);\n                  },\n                  disabled: uploading,\n                  style: {\n                    backgroundColor: '#f44336',\n                    boxShadow: '0 0 20px #f4433680, 0 4px 6px rgba(0, 0, 0, 0.1)'\n                  },\n                  children: \"Cancel\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 246,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 234,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 232,\n              columnNumber: 19\n            }, this), !selectedFile && /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"details-button upload-cancel-button\",\n              onClick: () => {\n                setShowUpload(false);\n                setSelectedFile(null);\n              },\n              disabled: uploading,\n              style: {\n                backgroundColor: '#f44336',\n                boxShadow: '0 0 20px #f4433680, 0 4px 6px rgba(0, 0, 0, 0.1)'\n              },\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 266,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 196,\n        columnNumber: 11\n      }, this), showDetails && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"character-details-overlay\",\n        onClick: () => setShowDetails(false),\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"character-details-content\",\n          onClick: e => e.stopPropagation() // Prevent clicks on content from closing\n          ,\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              style: {\n                color: getRarityColor(character.rarity)\n              },\n              children: \"Name:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 295,\n              columnNumber: 18\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 295,\n              columnNumber: 92\n            }, this), character.Name]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 295,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              style: {\n                color: getRarityColor(character.rarity)\n              },\n              children: \"Source:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 296,\n              columnNumber: 18\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 296,\n              columnNumber: 94\n            }, this), character.Source]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 296,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              style: {\n                color: getRarityColor(character.rarity)\n              },\n              children: \"Value:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 297,\n              columnNumber: 18\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 297,\n              columnNumber: 93\n            }, this), getRarityValue(character.rarity)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 297,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              style: {\n                color: getRarityColor(character.rarity)\n              },\n              children: \"What to look for:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 298,\n              columnNumber: 18\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 298,\n              columnNumber: 104\n            }, this), character.description || \"No description available\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 298,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              style: {\n                color: getRarityColor(character.rarity)\n              },\n              children: \"Special conditions:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 299,\n              columnNumber: 18\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 299,\n              columnNumber: 106\n            }, this), character.conditions || \"None\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 299,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 291,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 287,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 149,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 148,\n    columnNumber: 5\n  }, this);\n};\n_s(PortraitOverlay, \"+XvkU7TDF9hWkZrsoh0jQpxfuFc=\");\n_c = PortraitOverlay;\nexport default PortraitOverlay;\nvar _c;\n$RefreshReg$(_c, \"PortraitOverlay\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "jsxDEV", "_jsxDEV", "PortraitOverlay", "character", "onClose", "onClaim", "sourcePosition", "isClaimed", "isReadOnly", "userId", "boardId", "squareIndex", "userImage", "_s", "getPortraitUrl", "<PERSON><PERSON><PERSON>", "process", "env", "PUBLIC_URL", "portraitUrl", "Portrait", "getFrameOverlayUrl", "rarity", "frameOverlayUrl", "isVisible", "setIsVisible", "showDetails", "setShowDetails", "selectedFile", "setSelectedFile", "uploading", "setUploading", "showUpload", "setShowUpload", "fileInputRef", "timer", "setTimeout", "clearTimeout", "handleClose", "handleFileSelect", "event", "file", "target", "files", "handleUpload", "undefined", "formData", "FormData", "append", "response", "fetch", "method", "body", "ok", "result", "json", "image_path", "console", "error", "alert", "handleClaim", "handleToggleDetails", "getRarityValue", "getRarityColor", "className", "children", "style", "transform<PERSON><PERSON>in", "x", "y", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "src", "alt", "Name", "backgroundColor", "boxShadow", "e", "stopPropagation", "type", "ref", "onChange", "accept", "display", "_fileInputRef$current", "current", "click", "disabled", "color", "name", "Source", "description", "conditions", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/Projects/bimbo-hunter-base/client/src/components/Board/PortraitOverlay.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\r\n\r\nconst PortraitOverlay = ({ character, onClose, onClaim, sourcePosition, isClaimed, isReadOnly, userId, boardId, squareIndex, userImage }) => {\r\n  // Get the portrait URL from the portrait path\r\n  const getPortraitUrl = (portraitPath) => {\r\n    if (!portraitPath) return null;\r\n    return `${process.env.PUBLIC_URL}${portraitPath}`;\r\n  };\r\n\r\n  // Use user image if available and claimed, otherwise use original portrait\r\n  const portraitUrl = (isClaimed && userImage)\r\n    ? `${process.env.PUBLIC_URL}${userImage}`\r\n    : getPortraitUrl(character.Portrait);\r\n\r\n  // Get the frame overlay URL based on character rarity\r\n  const getFrameOverlayUrl = (rarity) => {\r\n    if (!rarity || rarity === 'FREE') return null; // No frame for FREE characters\r\n    return `${process.env.PUBLIC_URL}/frames/${rarity} - Portrait.png`;\r\n  };\r\n\r\n  const frameOverlayUrl = getFrameOverlayUrl(character.rarity);\r\n\r\n  // Use state to control animation classes and details visibility\r\n  const [isVisible, setIsVisible] = useState(false);\r\n  const [showDetails, setShowDetails] = useState(false);\r\n  const [selectedFile, setSelectedFile] = useState(null);\r\n  const [uploading, setUploading] = useState(false);\r\n  const [showUpload, setShowUpload] = useState(false);\r\n  const fileInputRef = useRef(null);\r\n\r\n  // Apply the animation after component mounts\r\n  useEffect(() => {\r\n    // Small delay to ensure the component is rendered before animation starts\r\n    const timer = setTimeout(() => {\r\n      setIsVisible(true);\r\n    }, 50);\r\n\r\n    return () => clearTimeout(timer);\r\n  }, []);\r\n\r\n  // Handle closing with animation\r\n  const handleClose = () => {\r\n    setIsVisible(false);\r\n    setShowDetails(false);\r\n    // Wait for animation to complete before actually closing\r\n    setTimeout(onClose, 300);\r\n  };\r\n\r\n  // Handle file selection\r\n  const handleFileSelect = (event) => {\r\n    const file = event.target.files[0];\r\n    if (file) {\r\n      setSelectedFile(file);\r\n      setShowUpload(true);\r\n    }\r\n  };\r\n\r\n  // Handle file upload\r\n  const handleUpload = async () => {\r\n    if (!selectedFile || !userId || !boardId || squareIndex === undefined) return;\r\n\r\n    setUploading(true);\r\n    const formData = new FormData();\r\n    formData.append('file', selectedFile);\r\n\r\n    try {\r\n      const response = await fetch(`http://localhost:5000/api/users/${userId}/boards/${boardId}/upload/${squareIndex}`, {\r\n        method: 'POST',\r\n        body: formData,\r\n      });\r\n\r\n      if (response.ok) {\r\n        const result = await response.json();\r\n        // Call onClaim with the uploaded image path\r\n        onClaim(result.image_path);\r\n        setIsVisible(false);\r\n        setShowDetails(false);\r\n        setShowUpload(false);\r\n      } else {\r\n        console.error('Upload failed');\r\n        alert('Failed to upload image. Please try again.');\r\n      }\r\n    } catch (error) {\r\n      console.error('Upload error:', error);\r\n      alert('Failed to upload image. Please try again.');\r\n    } finally {\r\n      setUploading(false);\r\n    }\r\n  };\r\n\r\n  // Handle claiming with animation (for unclaiming)\r\n  const handleClaim = () => {\r\n    if (isReadOnly) return; // Disable claiming for read-only mode\r\n\r\n    if (isClaimed) {\r\n      // If already claimed, just unclaim\r\n      setIsVisible(false);\r\n      setShowDetails(false);\r\n      setTimeout(() => onClaim(null), 300);\r\n    } else {\r\n      // If not claimed, show upload interface\r\n      setShowUpload(true);\r\n    }\r\n  };\r\n\r\n  // Handle showing/hiding details\r\n  const handleToggleDetails = () => {\r\n    setShowDetails(!showDetails);\r\n  };\r\n\r\n  // Get rarity value text\r\n  const getRarityValue = (rarity) => {\r\n    switch (rarity) {\r\n      case 'FREE':\r\n        return 'FREE (1 pt)';\r\n      case 'R':\r\n        return 'R (2 pts)';\r\n      case 'SR':\r\n        return 'SR (3 pts)';\r\n      case 'SSR':\r\n        return 'SSR (4 pts)';\r\n      case 'UR+':\r\n        return 'UR+ (6 pts)';\r\n      default:\r\n        return 'Unknown';\r\n    }\r\n  };\r\n\r\n  // Get rarity color for details button\r\n  const getRarityColor = (rarity) => {\r\n    switch (rarity) {\r\n      case 'FREE':\r\n        return '#4CAF50'; // Green\r\n      case 'R':\r\n        return '#2196F3'; // Blue\r\n      case 'SR':\r\n        return '#9C27B0'; // Purple\r\n      case 'SSR':\r\n        return '#FF9800'; // Orange\r\n      case 'UR+':\r\n        return '#F44336'; // Red\r\n      default:\r\n        return '#2196F3'; // Default blue\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className={`portrait-overlay ${isVisible ? 'visible' : ''}`}>\r\n      <div\r\n        className={`portrait-container ${isVisible ? 'visible' : ''}`}\r\n        style={sourcePosition ? {\r\n          // If we have source position, use it for initial transform origin\r\n          transformOrigin: `${sourcePosition.x}px ${sourcePosition.y}px`\r\n        } : {}}\r\n      >\r\n        <button className=\"close-button\" onClick={handleClose}>×</button>\r\n        <div className=\"portrait-image-container\">\r\n          <img\r\n            src={portraitUrl}\r\n            alt={character.Name}\r\n            className=\"character-portrait\"\r\n          />\r\n          {frameOverlayUrl && (\r\n            <img\r\n              src={frameOverlayUrl}\r\n              alt={`${character.rarity} frame`}\r\n              className=\"portrait-frame-overlay\"\r\n            />\r\n          )}\r\n        </div>\r\n        <div className=\"portrait-buttons\">\r\n          {!isReadOnly && !showUpload && (\r\n            <button\r\n              className={`claim-button ${isClaimed ? 'unclaim' : ''}`}\r\n              onClick={handleClaim}\r\n            >\r\n              {isClaimed ? 'Unclaim' : 'Claim!'}\r\n            </button>\r\n          )}\r\n          {!showUpload && (\r\n            <button\r\n              className=\"details-button\"\r\n              onClick={handleToggleDetails}\r\n              style={{\r\n                backgroundColor: getRarityColor(character.rarity),\r\n                boxShadow: `0 0 20px ${getRarityColor(character.rarity)}80, 0 4px 6px rgba(0, 0, 0, 0.1)`\r\n              }}\r\n            >\r\n              Details\r\n            </button>\r\n          )}\r\n        </div>\r\n\r\n        {/* Upload Interface Overlay */}\r\n        {showUpload && !isReadOnly && (\r\n          <div\r\n            className=\"character-details-overlay\"\r\n            onClick={() => {\r\n              setShowUpload(false);\r\n              setSelectedFile(null);\r\n            }}\r\n          >\r\n            <div\r\n              className=\"character-details-content upload-overlay-content\"\r\n              onClick={(e) => e.stopPropagation()} // Prevent clicks on content from closing\r\n            >\r\n              <h3 className=\"upload-title\">Upload Your Photo</h3>\r\n              <p className=\"upload-description\">Upload a photo of yourself with this cosplayer to claim this square!</p>\r\n\r\n              <input\r\n                type=\"file\"\r\n                ref={fileInputRef}\r\n                onChange={handleFileSelect}\r\n                accept=\"image/*\"\r\n                style={{ display: 'none' }}\r\n              />\r\n\r\n              <div className=\"upload-buttons\">\r\n                <button\r\n                  className=\"details-button upload-select-button\"\r\n                  onClick={() => fileInputRef.current?.click()}\r\n                  disabled={uploading}\r\n                  style={{\r\n                    backgroundColor: getRarityColor(character.rarity),\r\n                    boxShadow: `0 0 20px ${getRarityColor(character.rarity)}80, 0 4px 6px rgba(0, 0, 0, 0.1)`\r\n                  }}\r\n                >\r\n                  {selectedFile ? 'Change Photo' : 'Select Photo'}\r\n                </button>\r\n\r\n                {selectedFile && (\r\n                  <div className=\"selected-file-info\">\r\n                    <p><strong style={{ color: getRarityColor(character.rarity) }}>Selected:</strong><br />{selectedFile.name}</p>\r\n                    <div className=\"upload-actions\">\r\n                      <button\r\n                        className=\"details-button upload-confirm-button\"\r\n                        onClick={handleUpload}\r\n                        disabled={uploading}\r\n                        style={{\r\n                          backgroundColor: '#4CAF50',\r\n                          boxShadow: '0 0 20px #4CAF5080, 0 4px 6px rgba(0, 0, 0, 0.1)'\r\n                        }}\r\n                      >\r\n                        {uploading ? 'Uploading...' : 'Upload & Claim'}\r\n                      </button>\r\n                      <button\r\n                        className=\"details-button upload-cancel-button\"\r\n                        onClick={() => {\r\n                          setShowUpload(false);\r\n                          setSelectedFile(null);\r\n                        }}\r\n                        disabled={uploading}\r\n                        style={{\r\n                          backgroundColor: '#f44336',\r\n                          boxShadow: '0 0 20px #f4433680, 0 4px 6px rgba(0, 0, 0, 0.1)'\r\n                        }}\r\n                      >\r\n                        Cancel\r\n                      </button>\r\n                    </div>\r\n                  </div>\r\n                )}\r\n\r\n                {/* Cancel button always visible */}\r\n                {!selectedFile && (\r\n                  <button\r\n                    className=\"details-button upload-cancel-button\"\r\n                    onClick={() => {\r\n                      setShowUpload(false);\r\n                      setSelectedFile(null);\r\n                    }}\r\n                    disabled={uploading}\r\n                    style={{\r\n                      backgroundColor: '#f44336',\r\n                      boxShadow: '0 0 20px #f4433680, 0 4px 6px rgba(0, 0, 0, 0.1)'\r\n                    }}\r\n                  >\r\n                    Cancel\r\n                  </button>\r\n                )}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {showDetails && (\r\n          <div\r\n            className=\"character-details-overlay\"\r\n            onClick={() => setShowDetails(false)}\r\n          >\r\n            <div\r\n              className=\"character-details-content\"\r\n              onClick={(e) => e.stopPropagation()} // Prevent clicks on content from closing\r\n            >\r\n              <p><strong style={{ color: getRarityColor(character.rarity) }}>Name:</strong><br />{character.Name}</p>\r\n              <p><strong style={{ color: getRarityColor(character.rarity) }}>Source:</strong><br />{character.Source}</p>\r\n              <p><strong style={{ color: getRarityColor(character.rarity) }}>Value:</strong><br />{getRarityValue(character.rarity)}</p>\r\n              <p><strong style={{ color: getRarityColor(character.rarity) }}>What to look for:</strong><br />{character.description || \"No description available\"}</p>\r\n              <p><strong style={{ color: getRarityColor(character.rarity) }}>Special conditions:</strong><br />{character.conditions || \"None\"}</p>\r\n            </div>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default PortraitOverlay;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3D,MAAMC,eAAe,GAAGA,CAAC;EAAEC,SAAS;EAAEC,OAAO;EAAEC,OAAO;EAAEC,cAAc;EAAEC,SAAS;EAAEC,UAAU;EAAEC,MAAM;EAAEC,OAAO;EAAEC,WAAW;EAAEC;AAAU,CAAC,KAAK;EAAAC,EAAA;EAC3I;EACA,MAAMC,cAAc,GAAIC,YAAY,IAAK;IACvC,IAAI,CAACA,YAAY,EAAE,OAAO,IAAI;IAC9B,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,UAAU,GAAGH,YAAY,EAAE;EACnD,CAAC;;EAED;EACA,MAAMI,WAAW,GAAIZ,SAAS,IAAIK,SAAS,GACvC,GAAGI,OAAO,CAACC,GAAG,CAACC,UAAU,GAAGN,SAAS,EAAE,GACvCE,cAAc,CAACX,SAAS,CAACiB,QAAQ,CAAC;;EAEtC;EACA,MAAMC,kBAAkB,GAAIC,MAAM,IAAK;IACrC,IAAI,CAACA,MAAM,IAAIA,MAAM,KAAK,MAAM,EAAE,OAAO,IAAI,CAAC,CAAC;IAC/C,OAAO,GAAGN,OAAO,CAACC,GAAG,CAACC,UAAU,WAAWI,MAAM,iBAAiB;EACpE,CAAC;EAED,MAAMC,eAAe,GAAGF,kBAAkB,CAAClB,SAAS,CAACmB,MAAM,CAAC;;EAE5D;EACA,MAAM,CAACE,SAAS,EAAEC,YAAY,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC6B,WAAW,EAAEC,cAAc,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC+B,YAAY,EAAEC,eAAe,CAAC,GAAGhC,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACiC,SAAS,EAAEC,YAAY,CAAC,GAAGlC,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACmC,UAAU,EAAEC,aAAa,CAAC,GAAGpC,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAMqC,YAAY,GAAGnC,MAAM,CAAC,IAAI,CAAC;;EAEjC;EACAD,SAAS,CAAC,MAAM;IACd;IACA,MAAMqC,KAAK,GAAGC,UAAU,CAAC,MAAM;MAC7BX,YAAY,CAAC,IAAI,CAAC;IACpB,CAAC,EAAE,EAAE,CAAC;IAEN,OAAO,MAAMY,YAAY,CAACF,KAAK,CAAC;EAClC,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMG,WAAW,GAAGA,CAAA,KAAM;IACxBb,YAAY,CAAC,KAAK,CAAC;IACnBE,cAAc,CAAC,KAAK,CAAC;IACrB;IACAS,UAAU,CAAChC,OAAO,EAAE,GAAG,CAAC;EAC1B,CAAC;;EAED;EACA,MAAMmC,gBAAgB,GAAIC,KAAK,IAAK;IAClC,MAAMC,IAAI,GAAGD,KAAK,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAClC,IAAIF,IAAI,EAAE;MACRZ,eAAe,CAACY,IAAI,CAAC;MACrBR,aAAa,CAAC,IAAI,CAAC;IACrB;EACF,CAAC;;EAED;EACA,MAAMW,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI,CAAChB,YAAY,IAAI,CAACnB,MAAM,IAAI,CAACC,OAAO,IAAIC,WAAW,KAAKkC,SAAS,EAAE;IAEvEd,YAAY,CAAC,IAAI,CAAC;IAClB,MAAMe,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;IAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEpB,YAAY,CAAC;IAErC,IAAI;MACF,MAAMqB,QAAQ,GAAG,MAAMC,KAAK,CAAC,mCAAmCzC,MAAM,WAAWC,OAAO,WAAWC,WAAW,EAAE,EAAE;QAChHwC,MAAM,EAAE,MAAM;QACdC,IAAI,EAAEN;MACR,CAAC,CAAC;MAEF,IAAIG,QAAQ,CAACI,EAAE,EAAE;QACf,MAAMC,MAAM,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;QACpC;QACAlD,OAAO,CAACiD,MAAM,CAACE,UAAU,CAAC;QAC1B/B,YAAY,CAAC,KAAK,CAAC;QACnBE,cAAc,CAAC,KAAK,CAAC;QACrBM,aAAa,CAAC,KAAK,CAAC;MACtB,CAAC,MAAM;QACLwB,OAAO,CAACC,KAAK,CAAC,eAAe,CAAC;QAC9BC,KAAK,CAAC,2CAA2C,CAAC;MACpD;IACF,CAAC,CAAC,OAAOD,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;MACrCC,KAAK,CAAC,2CAA2C,CAAC;IACpD,CAAC,SAAS;MACR5B,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;;EAED;EACA,MAAM6B,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAIpD,UAAU,EAAE,OAAO,CAAC;;IAExB,IAAID,SAAS,EAAE;MACb;MACAkB,YAAY,CAAC,KAAK,CAAC;MACnBE,cAAc,CAAC,KAAK,CAAC;MACrBS,UAAU,CAAC,MAAM/B,OAAO,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC;IACtC,CAAC,MAAM;MACL;MACA4B,aAAa,CAAC,IAAI,CAAC;IACrB;EACF,CAAC;;EAED;EACA,MAAM4B,mBAAmB,GAAGA,CAAA,KAAM;IAChClC,cAAc,CAAC,CAACD,WAAW,CAAC;EAC9B,CAAC;;EAED;EACA,MAAMoC,cAAc,GAAIxC,MAAM,IAAK;IACjC,QAAQA,MAAM;MACZ,KAAK,MAAM;QACT,OAAO,aAAa;MACtB,KAAK,GAAG;QACN,OAAO,WAAW;MACpB,KAAK,IAAI;QACP,OAAO,YAAY;MACrB,KAAK,KAAK;QACR,OAAO,aAAa;MACtB,KAAK,KAAK;QACR,OAAO,aAAa;MACtB;QACE,OAAO,SAAS;IACpB;EACF,CAAC;;EAED;EACA,MAAMyC,cAAc,GAAIzC,MAAM,IAAK;IACjC,QAAQA,MAAM;MACZ,KAAK,MAAM;QACT,OAAO,SAAS;MAAE;MACpB,KAAK,GAAG;QACN,OAAO,SAAS;MAAE;MACpB,KAAK,IAAI;QACP,OAAO,SAAS;MAAE;MACpB,KAAK,KAAK;QACR,OAAO,SAAS;MAAE;MACpB,KAAK,KAAK;QACR,OAAO,SAAS;MAAE;MACpB;QACE,OAAO,SAAS;MAAE;IACtB;EACF,CAAC;EAED,oBACErB,OAAA;IAAK+D,SAAS,EAAE,oBAAoBxC,SAAS,GAAG,SAAS,GAAG,EAAE,EAAG;IAAAyC,QAAA,eAC/DhE,OAAA;MACE+D,SAAS,EAAE,sBAAsBxC,SAAS,GAAG,SAAS,GAAG,EAAE,EAAG;MAC9D0C,KAAK,EAAE5D,cAAc,GAAG;QACtB;QACA6D,eAAe,EAAE,GAAG7D,cAAc,CAAC8D,CAAC,MAAM9D,cAAc,CAAC+D,CAAC;MAC5D,CAAC,GAAG,CAAC,CAAE;MAAAJ,QAAA,gBAEPhE,OAAA;QAAQ+D,SAAS,EAAC,cAAc;QAACM,OAAO,EAAEhC,WAAY;QAAA2B,QAAA,EAAC;MAAC;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACjEzE,OAAA;QAAK+D,SAAS,EAAC,0BAA0B;QAAAC,QAAA,gBACvChE,OAAA;UACE0E,GAAG,EAAExD,WAAY;UACjByD,GAAG,EAAEzE,SAAS,CAAC0E,IAAK;UACpBb,SAAS,EAAC;QAAoB;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC,EACDnD,eAAe,iBACdtB,OAAA;UACE0E,GAAG,EAAEpD,eAAgB;UACrBqD,GAAG,EAAE,GAAGzE,SAAS,CAACmB,MAAM,QAAS;UACjC0C,SAAS,EAAC;QAAwB;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CACF;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACNzE,OAAA;QAAK+D,SAAS,EAAC,kBAAkB;QAAAC,QAAA,GAC9B,CAACzD,UAAU,IAAI,CAACwB,UAAU,iBACzB/B,OAAA;UACE+D,SAAS,EAAE,gBAAgBzD,SAAS,GAAG,SAAS,GAAG,EAAE,EAAG;UACxD+D,OAAO,EAAEV,WAAY;UAAAK,QAAA,EAEpB1D,SAAS,GAAG,SAAS,GAAG;QAAQ;UAAAgE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CACT,EACA,CAAC1C,UAAU,iBACV/B,OAAA;UACE+D,SAAS,EAAC,gBAAgB;UAC1BM,OAAO,EAAET,mBAAoB;UAC7BK,KAAK,EAAE;YACLY,eAAe,EAAEf,cAAc,CAAC5D,SAAS,CAACmB,MAAM,CAAC;YACjDyD,SAAS,EAAE,YAAYhB,cAAc,CAAC5D,SAAS,CAACmB,MAAM,CAAC;UACzD,CAAE;UAAA2C,QAAA,EACH;QAED;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EAGL1C,UAAU,IAAI,CAACxB,UAAU,iBACxBP,OAAA;QACE+D,SAAS,EAAC,2BAA2B;QACrCM,OAAO,EAAEA,CAAA,KAAM;UACbrC,aAAa,CAAC,KAAK,CAAC;UACpBJ,eAAe,CAAC,IAAI,CAAC;QACvB,CAAE;QAAAoC,QAAA,eAEFhE,OAAA;UACE+D,SAAS,EAAC,kDAAkD;UAC5DM,OAAO,EAAGU,CAAC,IAAKA,CAAC,CAACC,eAAe,CAAC,CAAE,CAAC;UAAA;UAAAhB,QAAA,gBAErChE,OAAA;YAAI+D,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAAiB;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnDzE,OAAA;YAAG+D,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAAC;UAAoE;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAE1GzE,OAAA;YACEiF,IAAI,EAAC,MAAM;YACXC,GAAG,EAAEjD,YAAa;YAClBkD,QAAQ,EAAE7C,gBAAiB;YAC3B8C,MAAM,EAAC,SAAS;YAChBnB,KAAK,EAAE;cAAEoB,OAAO,EAAE;YAAO;UAAE;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC,eAEFzE,OAAA;YAAK+D,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BhE,OAAA;cACE+D,SAAS,EAAC,qCAAqC;cAC/CM,OAAO,EAAEA,CAAA;gBAAA,IAAAiB,qBAAA;gBAAA,QAAAA,qBAAA,GAAMrD,YAAY,CAACsD,OAAO,cAAAD,qBAAA,uBAApBA,qBAAA,CAAsBE,KAAK,CAAC,CAAC;cAAA,CAAC;cAC7CC,QAAQ,EAAE5D,SAAU;cACpBoC,KAAK,EAAE;gBACLY,eAAe,EAAEf,cAAc,CAAC5D,SAAS,CAACmB,MAAM,CAAC;gBACjDyD,SAAS,EAAE,YAAYhB,cAAc,CAAC5D,SAAS,CAACmB,MAAM,CAAC;cACzD,CAAE;cAAA2C,QAAA,EAEDrC,YAAY,GAAG,cAAc,GAAG;YAAc;cAAA2C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC,CAAC,EAER9C,YAAY,iBACX3B,OAAA;cAAK+D,SAAS,EAAC,oBAAoB;cAAAC,QAAA,gBACjChE,OAAA;gBAAAgE,QAAA,gBAAGhE,OAAA;kBAAQiE,KAAK,EAAE;oBAAEyB,KAAK,EAAE5B,cAAc,CAAC5D,SAAS,CAACmB,MAAM;kBAAE,CAAE;kBAAA2C,QAAA,EAAC;gBAAS;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAAAzE,OAAA;kBAAAsE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,EAAC9C,YAAY,CAACgE,IAAI;cAAA;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC9GzE,OAAA;gBAAK+D,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC7BhE,OAAA;kBACE+D,SAAS,EAAC,sCAAsC;kBAChDM,OAAO,EAAE1B,YAAa;kBACtB8C,QAAQ,EAAE5D,SAAU;kBACpBoC,KAAK,EAAE;oBACLY,eAAe,EAAE,SAAS;oBAC1BC,SAAS,EAAE;kBACb,CAAE;kBAAAd,QAAA,EAEDnC,SAAS,GAAG,cAAc,GAAG;gBAAgB;kBAAAyC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC,CAAC,eACTzE,OAAA;kBACE+D,SAAS,EAAC,qCAAqC;kBAC/CM,OAAO,EAAEA,CAAA,KAAM;oBACbrC,aAAa,CAAC,KAAK,CAAC;oBACpBJ,eAAe,CAAC,IAAI,CAAC;kBACvB,CAAE;kBACF6D,QAAQ,EAAE5D,SAAU;kBACpBoC,KAAK,EAAE;oBACLY,eAAe,EAAE,SAAS;oBAC1BC,SAAS,EAAE;kBACb,CAAE;kBAAAd,QAAA,EACH;gBAED;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN,EAGA,CAAC9C,YAAY,iBACZ3B,OAAA;cACE+D,SAAS,EAAC,qCAAqC;cAC/CM,OAAO,EAAEA,CAAA,KAAM;gBACbrC,aAAa,CAAC,KAAK,CAAC;gBACpBJ,eAAe,CAAC,IAAI,CAAC;cACvB,CAAE;cACF6D,QAAQ,EAAE5D,SAAU;cACpBoC,KAAK,EAAE;gBACLY,eAAe,EAAE,SAAS;gBAC1BC,SAAS,EAAE;cACb,CAAE;cAAAd,QAAA,EACH;YAED;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CACT;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAEAhD,WAAW,iBACVzB,OAAA;QACE+D,SAAS,EAAC,2BAA2B;QACrCM,OAAO,EAAEA,CAAA,KAAM3C,cAAc,CAAC,KAAK,CAAE;QAAAsC,QAAA,eAErChE,OAAA;UACE+D,SAAS,EAAC,2BAA2B;UACrCM,OAAO,EAAGU,CAAC,IAAKA,CAAC,CAACC,eAAe,CAAC,CAAE,CAAC;UAAA;UAAAhB,QAAA,gBAErChE,OAAA;YAAAgE,QAAA,gBAAGhE,OAAA;cAAQiE,KAAK,EAAE;gBAAEyB,KAAK,EAAE5B,cAAc,CAAC5D,SAAS,CAACmB,MAAM;cAAE,CAAE;cAAA2C,QAAA,EAAC;YAAK;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAAAzE,OAAA;cAAAsE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,EAACvE,SAAS,CAAC0E,IAAI;UAAA;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACvGzE,OAAA;YAAAgE,QAAA,gBAAGhE,OAAA;cAAQiE,KAAK,EAAE;gBAAEyB,KAAK,EAAE5B,cAAc,CAAC5D,SAAS,CAACmB,MAAM;cAAE,CAAE;cAAA2C,QAAA,EAAC;YAAO;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAAAzE,OAAA;cAAAsE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,EAACvE,SAAS,CAAC0F,MAAM;UAAA;YAAAtB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3GzE,OAAA;YAAAgE,QAAA,gBAAGhE,OAAA;cAAQiE,KAAK,EAAE;gBAAEyB,KAAK,EAAE5B,cAAc,CAAC5D,SAAS,CAACmB,MAAM;cAAE,CAAE;cAAA2C,QAAA,EAAC;YAAM;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAAAzE,OAAA;cAAAsE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,EAACZ,cAAc,CAAC3D,SAAS,CAACmB,MAAM,CAAC;UAAA;YAAAiD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1HzE,OAAA;YAAAgE,QAAA,gBAAGhE,OAAA;cAAQiE,KAAK,EAAE;gBAAEyB,KAAK,EAAE5B,cAAc,CAAC5D,SAAS,CAACmB,MAAM;cAAE,CAAE;cAAA2C,QAAA,EAAC;YAAiB;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAAAzE,OAAA;cAAAsE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,EAACvE,SAAS,CAAC2F,WAAW,IAAI,0BAA0B;UAAA;YAAAvB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxJzE,OAAA;YAAAgE,QAAA,gBAAGhE,OAAA;cAAQiE,KAAK,EAAE;gBAAEyB,KAAK,EAAE5B,cAAc,CAAC5D,SAAS,CAACmB,MAAM;cAAE,CAAE;cAAA2C,QAAA,EAAC;YAAmB;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAAAzE,OAAA;cAAAsE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,EAACvE,SAAS,CAAC4F,UAAU,IAAI,MAAM;UAAA;YAAAxB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC7D,EAAA,CA/SIX,eAAe;AAAA8F,EAAA,GAAf9F,eAAe;AAiTrB,eAAeA,eAAe;AAAC,IAAA8F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
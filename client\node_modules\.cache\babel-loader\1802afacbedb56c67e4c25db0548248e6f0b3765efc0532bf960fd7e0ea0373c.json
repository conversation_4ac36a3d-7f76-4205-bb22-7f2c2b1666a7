{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\bimbo-hunter-base\\\\client\\\\src\\\\components\\\\Board\\\\PointsDisplay.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PointsDisplay = ({\n  characters,\n  markedCells,\n  onRefreshClick,\n  isReadOnly,\n  score\n}) => {\n  _s();\n  const [groupPoints, setGroupPoints] = useState(0);\n\n  // Fetch group points\n  useEffect(() => {\n    const fetchGroupPoints = async () => {\n      try {\n        const response = await fetch('http://localhost:5000/api/group-points');\n        if (response.ok) {\n          const data = await response.json();\n          setGroupPoints(data.total_points);\n        }\n      } catch (error) {\n        console.error('Error fetching group points:', error);\n      }\n    };\n    fetchGroupPoints();\n\n    // Set up polling to refresh group points every 30 seconds\n    const intervalId = setInterval(fetchGroupPoints, 30000);\n    return () => clearInterval(intervalId); // Clean up on unmount\n  }, []);\n\n  // Also update group points when the individual score changes\n  useEffect(() => {\n    const fetchGroupPoints = async () => {\n      try {\n        const response = await fetch('http://localhost:5000/api/group-points');\n        if (response.ok) {\n          const data = await response.json();\n          setGroupPoints(data.total_points);\n        }\n      } catch (error) {\n        console.error('Error fetching group points:', error);\n      }\n    };\n\n    // Only fetch if we have a score (to avoid unnecessary calls on initial load)\n    if (score !== undefined) {\n      fetchGroupPoints();\n    }\n  }, [score]);\n\n  // Point values for each rarity\n  const RARITY_POINTS = {\n    \"FREE\": 1,\n    \"R\": 2,\n    \"SR\": 3,\n    \"SSR\": 4,\n    \"UR+\": 6\n  };\n\n  // Calculate points from marked cells\n  const calculateBasePoints = () => {\n    let totalPoints = 0;\n    markedCells.forEach(index => {\n      if (index >= 0 && index < characters.length) {\n        const character = characters[index];\n        totalPoints += RARITY_POINTS[character.rarity];\n      }\n    });\n    return totalPoints;\n  };\n\n  // Check if there's a bingo (5 in a row, column, or diagonal)\n  const checkForBingos = () => {\n    const bingoBonus = 5; // Bonus points for each bingo\n    let bingoCount = 0;\n\n    // Convert markedCells set to a 5x5 grid for easier checking\n    const grid = Array(5).fill().map(() => Array(5).fill(false));\n    markedCells.forEach(index => {\n      const row = Math.floor(index / 5);\n      const col = index % 5;\n      grid[row][col] = true;\n    });\n\n    // Check rows\n    for (let row = 0; row < 5; row++) {\n      if (grid[row].every(cell => cell)) {\n        bingoCount++;\n      }\n    }\n\n    // Check columns\n    for (let col = 0; col < 5; col++) {\n      if (grid.every(row => row[col])) {\n        bingoCount++;\n      }\n    }\n\n    // Check main diagonal (top-left to bottom-right)\n    if (grid[0][0] && grid[1][1] && grid[2][2] && grid[3][3] && grid[4][4]) {\n      bingoCount++;\n    }\n\n    // Check other diagonal (top-right to bottom-left)\n    if (grid[0][4] && grid[1][3] && grid[2][2] && grid[3][1] && grid[4][0]) {\n      bingoCount++;\n    }\n    return bingoCount * bingoBonus;\n  };\n  const basePoints = calculateBasePoints();\n  const bingoPoints = checkForBingos();\n  const totalPoints = basePoints + bingoPoints;\n\n  // Use the score from props if provided, otherwise calculate it\n  const displayScore = score !== undefined ? score : totalPoints;\n\n  // Calculate the percentage for the meter using GROUP POINTS (0-500 points)\n  const MAX_POINTS = 500; // Updated to 500 as per requirements\n  const fillPercentage = Math.min(groupPoints / MAX_POINTS * 100, 100);\n\n  // Milestone definitions - updated as per requirements\n  const milestones = [{\n    points: 150,\n    reward: \"Champagne Toast!\"\n  }, {\n    points: 250,\n    reward: \"Mystery Prize\"\n  }, {\n    points: 350,\n    reward: \"Seafood Boil\"\n  }];\n\n  // Determine the next reward based on current group points\n  const getNextReward = () => {\n    for (const milestone of milestones) {\n      if (groupPoints < milestone.points) {\n        return `${milestone.points} pts - ${milestone.reward}`;\n      }\n    }\n    return \"All rewards unlocked!\";\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"points-controls-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"points-meter-container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"points-meter\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"points-meter-fill\",\n          style: {\n            width: `${fillPercentage}%`\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"progress-text\",\n          children: \"GROUP POINTS\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 11\n        }, this), milestones.map((milestone, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"milestone-marker\",\n          style: {\n            left: `${milestone.points / MAX_POINTS * 100}%`\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"milestone-value\",\n            children: milestone.points\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 158,\n            columnNumber: 15\n          }, this)\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 153,\n          columnNumber: 13\n        }, this))]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 140,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 139,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"next-reward-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"next-reward-label\",\n        children: \"Next Reward:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 166,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"next-reward-text\",\n        children: getNextReward()\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 167,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 165,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"controls-wrapper\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"points-display\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"score-label\",\n          children: \"Score\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 172,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"score-value\",\n          children: displayScore\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 173,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 171,\n        columnNumber: 9\n      }, this), !isReadOnly && /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"refresh-button\",\n        onClick: onRefreshClick,\n        title: \"Refresh Board\",\n        children: \"\\u21BB\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 176,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 170,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 138,\n    columnNumber: 5\n  }, this);\n};\n_s(PointsDisplay, \"JsgCVVAYff1EguioLk1Z8z2CXtM=\");\n_c = PointsDisplay;\nexport default PointsDisplay;\nvar _c;\n$RefreshReg$(_c, \"PointsDisplay\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "jsxDEV", "_jsxDEV", "PointsDisplay", "characters", "<PERSON><PERSON><PERSON><PERSON>", "onRefreshClick", "isReadOnly", "score", "_s", "groupPoints", "setGroupPoints", "fetchGroupPoints", "response", "fetch", "ok", "data", "json", "total_points", "error", "console", "intervalId", "setInterval", "clearInterval", "undefined", "RARITY_POINTS", "calculateBasePoints", "totalPoints", "for<PERSON>ach", "index", "length", "character", "rarity", "checkForBingos", "bingoBonus", "bingoCount", "grid", "Array", "fill", "map", "row", "Math", "floor", "col", "every", "cell", "basePoints", "bingoPoints", "displayScore", "MAX_POINTS", "fillPercentage", "min", "milestones", "points", "reward", "getNextReward", "milestone", "className", "children", "style", "width", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "left", "onClick", "title", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/Projects/bimbo-hunter-base/client/src/components/Board/PointsDisplay.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\n\r\nconst PointsDisplay = ({ characters, marked<PERSON><PERSON>s, onRefreshClick, isReadOnly, score }) => {\r\n  const [groupPoints, setGroupPoints] = useState(0);\r\n\r\n  // Fetch group points\r\n  useEffect(() => {\r\n    const fetchGroupPoints = async () => {\r\n      try {\r\n        const response = await fetch('http://localhost:5000/api/group-points');\r\n        if (response.ok) {\r\n          const data = await response.json();\r\n          setGroupPoints(data.total_points);\r\n        }\r\n      } catch (error) {\r\n        console.error('Error fetching group points:', error);\r\n      }\r\n    };\r\n\r\n    fetchGroupPoints();\r\n\r\n    // Set up polling to refresh group points every 30 seconds\r\n    const intervalId = setInterval(fetchGroupPoints, 30000);\r\n\r\n    return () => clearInterval(intervalId); // Clean up on unmount\r\n  }, []);\r\n\r\n  // Also update group points when the individual score changes\r\n  useEffect(() => {\r\n    const fetchGroupPoints = async () => {\r\n      try {\r\n        const response = await fetch('http://localhost:5000/api/group-points');\r\n        if (response.ok) {\r\n          const data = await response.json();\r\n          setGroupPoints(data.total_points);\r\n        }\r\n      } catch (error) {\r\n        console.error('Error fetching group points:', error);\r\n      }\r\n    };\r\n\r\n    // Only fetch if we have a score (to avoid unnecessary calls on initial load)\r\n    if (score !== undefined) {\r\n      fetchGroupPoints();\r\n    }\r\n  }, [score]);\r\n\r\n  // Point values for each rarity\r\n  const RARITY_POINTS = {\r\n    \"FREE\": 1,\r\n    \"R\": 2,\r\n    \"SR\": 3,\r\n    \"SSR\": 4,\r\n    \"UR+\": 6\r\n  };\r\n\r\n  // Calculate points from marked cells\r\n  const calculateBasePoints = () => {\r\n    let totalPoints = 0;\r\n    markedCells.forEach(index => {\r\n      if (index >= 0 && index < characters.length) {\r\n        const character = characters[index];\r\n        totalPoints += RARITY_POINTS[character.rarity];\r\n      }\r\n    });\r\n    return totalPoints;\r\n  };\r\n\r\n  // Check if there's a bingo (5 in a row, column, or diagonal)\r\n  const checkForBingos = () => {\r\n    const bingoBonus = 5; // Bonus points for each bingo\r\n    let bingoCount = 0;\r\n\r\n    // Convert markedCells set to a 5x5 grid for easier checking\r\n    const grid = Array(5).fill().map(() => Array(5).fill(false));\r\n    markedCells.forEach(index => {\r\n      const row = Math.floor(index / 5);\r\n      const col = index % 5;\r\n      grid[row][col] = true;\r\n    });\r\n\r\n    // Check rows\r\n    for (let row = 0; row < 5; row++) {\r\n      if (grid[row].every(cell => cell)) {\r\n        bingoCount++;\r\n      }\r\n    }\r\n\r\n    // Check columns\r\n    for (let col = 0; col < 5; col++) {\r\n      if (grid.every(row => row[col])) {\r\n        bingoCount++;\r\n      }\r\n    }\r\n\r\n    // Check main diagonal (top-left to bottom-right)\r\n    if (grid[0][0] && grid[1][1] && grid[2][2] && grid[3][3] && grid[4][4]) {\r\n      bingoCount++;\r\n    }\r\n\r\n    // Check other diagonal (top-right to bottom-left)\r\n    if (grid[0][4] && grid[1][3] && grid[2][2] && grid[3][1] && grid[4][0]) {\r\n      bingoCount++;\r\n    }\r\n\r\n    return bingoCount * bingoBonus;\r\n  };\r\n\r\n  const basePoints = calculateBasePoints();\r\n  const bingoPoints = checkForBingos();\r\n  const totalPoints = basePoints + bingoPoints;\r\n\r\n  // Use the score from props if provided, otherwise calculate it\r\n  const displayScore = score !== undefined ? score : totalPoints;\r\n\r\n  // Calculate the percentage for the meter using GROUP POINTS (0-500 points)\r\n  const MAX_POINTS = 500; // Updated to 500 as per requirements\r\n  const fillPercentage = Math.min((groupPoints / MAX_POINTS) * 100, 100);\r\n\r\n  // Milestone definitions - updated as per requirements\r\n  const milestones = [\r\n    { points: 150, reward: \"Champagne Toast!\" },\r\n    { points: 250, reward: \"Mystery Prize\" },\r\n    { points: 350, reward: \"Seafood Boil\" }\r\n  ];\r\n\r\n  // Determine the next reward based on current group points\r\n  const getNextReward = () => {\r\n    for (const milestone of milestones) {\r\n      if (groupPoints < milestone.points) {\r\n        return `${milestone.points} pts - ${milestone.reward}`;\r\n      }\r\n    }\r\n    return \"All rewards unlocked!\";\r\n  };\r\n\r\n  return (\r\n    <div className=\"points-controls-container\">\r\n      <div className=\"points-meter-container\">\r\n        <div className=\"points-meter\">\r\n          <div\r\n            className=\"points-meter-fill\"\r\n            style={{ width: `${fillPercentage}%` }}\r\n          ></div>\r\n\r\n          {/* Add the progress text with negative coloring effect */}\r\n          <div className=\"progress-text\">\r\n            GROUP POINTS\r\n          </div>\r\n\r\n          {/* Render milestone markers (without tooltips) */}\r\n          {milestones.map((milestone, index) => (\r\n            <div\r\n              key={index}\r\n              className=\"milestone-marker\"\r\n              style={{ left: `${(milestone.points / MAX_POINTS) * 100}%` }}\r\n            >\r\n              <div className=\"milestone-value\">{milestone.points}</div>\r\n            </div>\r\n          ))}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Next Reward Section */}\r\n      <div className=\"next-reward-section\">\r\n        <div className=\"next-reward-label\">Next Reward:</div>\r\n        <div className=\"next-reward-text\">{getNextReward()}</div>\r\n      </div>\r\n\r\n      <div className=\"controls-wrapper\">\r\n        <div className=\"points-display\">\r\n          <div className=\"score-label\">Score</div>\r\n          <div className=\"score-value\">{displayScore}</div>\r\n        </div>\r\n        {!isReadOnly && (\r\n          <button \r\n            className=\"refresh-button\" \r\n            onClick={onRefreshClick} \r\n            title=\"Refresh Board\"\r\n          >\r\n            ↻\r\n          </button>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default PointsDisplay;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnD,MAAMC,aAAa,GAAGA,CAAC;EAAEC,UAAU;EAAEC,WAAW;EAAEC,cAAc;EAAEC,UAAU;EAAEC;AAAM,CAAC,KAAK;EAAAC,EAAA;EACxF,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGZ,QAAQ,CAAC,CAAC,CAAC;;EAEjD;EACAC,SAAS,CAAC,MAAM;IACd,MAAMY,gBAAgB,GAAG,MAAAA,CAAA,KAAY;MACnC,IAAI;QACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,wCAAwC,CAAC;QACtE,IAAID,QAAQ,CAACE,EAAE,EAAE;UACf,MAAMC,IAAI,GAAG,MAAMH,QAAQ,CAACI,IAAI,CAAC,CAAC;UAClCN,cAAc,CAACK,IAAI,CAACE,YAAY,CAAC;QACnC;MACF,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACtD;IACF,CAAC;IAEDP,gBAAgB,CAAC,CAAC;;IAElB;IACA,MAAMS,UAAU,GAAGC,WAAW,CAACV,gBAAgB,EAAE,KAAK,CAAC;IAEvD,OAAO,MAAMW,aAAa,CAACF,UAAU,CAAC,CAAC,CAAC;EAC1C,CAAC,EAAE,EAAE,CAAC;;EAEN;EACArB,SAAS,CAAC,MAAM;IACd,MAAMY,gBAAgB,GAAG,MAAAA,CAAA,KAAY;MACnC,IAAI;QACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,wCAAwC,CAAC;QACtE,IAAID,QAAQ,CAACE,EAAE,EAAE;UACf,MAAMC,IAAI,GAAG,MAAMH,QAAQ,CAACI,IAAI,CAAC,CAAC;UAClCN,cAAc,CAACK,IAAI,CAACE,YAAY,CAAC;QACnC;MACF,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACtD;IACF,CAAC;;IAED;IACA,IAAIX,KAAK,KAAKgB,SAAS,EAAE;MACvBZ,gBAAgB,CAAC,CAAC;IACpB;EACF,CAAC,EAAE,CAACJ,KAAK,CAAC,CAAC;;EAEX;EACA,MAAMiB,aAAa,GAAG;IACpB,MAAM,EAAE,CAAC;IACT,GAAG,EAAE,CAAC;IACN,IAAI,EAAE,CAAC;IACP,KAAK,EAAE,CAAC;IACR,KAAK,EAAE;EACT,CAAC;;EAED;EACA,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;IAChC,IAAIC,WAAW,GAAG,CAAC;IACnBtB,WAAW,CAACuB,OAAO,CAACC,KAAK,IAAI;MAC3B,IAAIA,KAAK,IAAI,CAAC,IAAIA,KAAK,GAAGzB,UAAU,CAAC0B,MAAM,EAAE;QAC3C,MAAMC,SAAS,GAAG3B,UAAU,CAACyB,KAAK,CAAC;QACnCF,WAAW,IAAIF,aAAa,CAACM,SAAS,CAACC,MAAM,CAAC;MAChD;IACF,CAAC,CAAC;IACF,OAAOL,WAAW;EACpB,CAAC;;EAED;EACA,MAAMM,cAAc,GAAGA,CAAA,KAAM;IAC3B,MAAMC,UAAU,GAAG,CAAC,CAAC,CAAC;IACtB,IAAIC,UAAU,GAAG,CAAC;;IAElB;IACA,MAAMC,IAAI,GAAGC,KAAK,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC,CAACC,GAAG,CAAC,MAAMF,KAAK,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC5DjC,WAAW,CAACuB,OAAO,CAACC,KAAK,IAAI;MAC3B,MAAMW,GAAG,GAAGC,IAAI,CAACC,KAAK,CAACb,KAAK,GAAG,CAAC,CAAC;MACjC,MAAMc,GAAG,GAAGd,KAAK,GAAG,CAAC;MACrBO,IAAI,CAACI,GAAG,CAAC,CAACG,GAAG,CAAC,GAAG,IAAI;IACvB,CAAC,CAAC;;IAEF;IACA,KAAK,IAAIH,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAG,CAAC,EAAEA,GAAG,EAAE,EAAE;MAChC,IAAIJ,IAAI,CAACI,GAAG,CAAC,CAACI,KAAK,CAACC,IAAI,IAAIA,IAAI,CAAC,EAAE;QACjCV,UAAU,EAAE;MACd;IACF;;IAEA;IACA,KAAK,IAAIQ,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAG,CAAC,EAAEA,GAAG,EAAE,EAAE;MAChC,IAAIP,IAAI,CAACQ,KAAK,CAACJ,GAAG,IAAIA,GAAG,CAACG,GAAG,CAAC,CAAC,EAAE;QAC/BR,UAAU,EAAE;MACd;IACF;;IAEA;IACA,IAAIC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAIA,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAIA,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAIA,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAIA,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACtED,UAAU,EAAE;IACd;;IAEA;IACA,IAAIC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAIA,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAIA,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAIA,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAIA,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACtED,UAAU,EAAE;IACd;IAEA,OAAOA,UAAU,GAAGD,UAAU;EAChC,CAAC;EAED,MAAMY,UAAU,GAAGpB,mBAAmB,CAAC,CAAC;EACxC,MAAMqB,WAAW,GAAGd,cAAc,CAAC,CAAC;EACpC,MAAMN,WAAW,GAAGmB,UAAU,GAAGC,WAAW;;EAE5C;EACA,MAAMC,YAAY,GAAGxC,KAAK,KAAKgB,SAAS,GAAGhB,KAAK,GAAGmB,WAAW;;EAE9D;EACA,MAAMsB,UAAU,GAAG,GAAG,CAAC,CAAC;EACxB,MAAMC,cAAc,GAAGT,IAAI,CAACU,GAAG,CAAEzC,WAAW,GAAGuC,UAAU,GAAI,GAAG,EAAE,GAAG,CAAC;;EAEtE;EACA,MAAMG,UAAU,GAAG,CACjB;IAAEC,MAAM,EAAE,GAAG;IAAEC,MAAM,EAAE;EAAmB,CAAC,EAC3C;IAAED,MAAM,EAAE,GAAG;IAAEC,MAAM,EAAE;EAAgB,CAAC,EACxC;IAAED,MAAM,EAAE,GAAG;IAAEC,MAAM,EAAE;EAAe,CAAC,CACxC;;EAED;EACA,MAAMC,aAAa,GAAGA,CAAA,KAAM;IAC1B,KAAK,MAAMC,SAAS,IAAIJ,UAAU,EAAE;MAClC,IAAI1C,WAAW,GAAG8C,SAAS,CAACH,MAAM,EAAE;QAClC,OAAO,GAAGG,SAAS,CAACH,MAAM,UAAUG,SAAS,CAACF,MAAM,EAAE;MACxD;IACF;IACA,OAAO,uBAAuB;EAChC,CAAC;EAED,oBACEpD,OAAA;IAAKuD,SAAS,EAAC,2BAA2B;IAAAC,QAAA,gBACxCxD,OAAA;MAAKuD,SAAS,EAAC,wBAAwB;MAAAC,QAAA,eACrCxD,OAAA;QAAKuD,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BxD,OAAA;UACEuD,SAAS,EAAC,mBAAmB;UAC7BE,KAAK,EAAE;YAAEC,KAAK,EAAE,GAAGV,cAAc;UAAI;QAAE;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC,eAGP9D,OAAA;UAAKuD,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAE/B;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,EAGLZ,UAAU,CAACb,GAAG,CAAC,CAACiB,SAAS,EAAE3B,KAAK,kBAC/B3B,OAAA;UAEEuD,SAAS,EAAC,kBAAkB;UAC5BE,KAAK,EAAE;YAAEM,IAAI,EAAE,GAAIT,SAAS,CAACH,MAAM,GAAGJ,UAAU,GAAI,GAAG;UAAI,CAAE;UAAAS,QAAA,eAE7DxD,OAAA;YAAKuD,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAAEF,SAAS,CAACH;UAAM;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC,GAJpDnC,KAAK;UAAAgC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAKP,CACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN9D,OAAA;MAAKuD,SAAS,EAAC,qBAAqB;MAAAC,QAAA,gBAClCxD,OAAA;QAAKuD,SAAS,EAAC,mBAAmB;QAAAC,QAAA,EAAC;MAAY;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACrD9D,OAAA;QAAKuD,SAAS,EAAC,kBAAkB;QAAAC,QAAA,EAAEH,aAAa,CAAC;MAAC;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtD,CAAC,eAEN9D,OAAA;MAAKuD,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/BxD,OAAA;QAAKuD,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BxD,OAAA;UAAKuD,SAAS,EAAC,aAAa;UAAAC,QAAA,EAAC;QAAK;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACxC9D,OAAA;UAAKuD,SAAS,EAAC,aAAa;UAAAC,QAAA,EAAEV;QAAY;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9C,CAAC,EACL,CAACzD,UAAU,iBACVL,OAAA;QACEuD,SAAS,EAAC,gBAAgB;QAC1BS,OAAO,EAAE5D,cAAe;QACxB6D,KAAK,EAAC,eAAe;QAAAT,QAAA,EACtB;MAED;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CACT;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACvD,EAAA,CAxLIN,aAAa;AAAAiE,EAAA,GAAbjE,aAAa;AA0LnB,eAAeA,aAAa;AAAC,IAAAiE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
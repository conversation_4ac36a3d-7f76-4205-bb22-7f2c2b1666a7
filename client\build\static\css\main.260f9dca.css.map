{"version": 3, "file": "static/css/main.260f9dca.css", "mappings": "AAAA,KAKE,kCAAmC,CACnC,iCAAkC,CAJlC,mIAEY,CAHZ,QAMF,CAEA,KACE,uEAEF,CCXA,WACE,oBAAuB,CAGvB,iBAAkB,CADlB,eAAmB,CADnB,8GAGF,CAEA,WACE,mBAAsB,CAGtB,iBAAkB,CADlB,eAAmB,CADnB,2GAGF,CAGA,eAGE,wDAA8D,CAM9D,qBAAsB,CAJtB,qBAAsB,CAKtB,gIAAmJ,CAHnJ,0BAA2B,CAN3B,gBAAiB,CAUjB,iBAAkB,CAHlB,qBAIF,CAGA,+BATE,kBAAmB,CAFnB,YAAa,CAFb,UAoBF,CAPA,gBAEE,sBAAuB,CAEvB,eAAgB,CAEhB,cACF,CAEA,qBACE,0BAA0C,CAC1C,iBAAkB,CAIlB,8DAAmF,CADnF,eAAgB,CAFhB,YAAa,CACb,UAGF,CAEA,wBAEE,UAAY,CADZ,+BAAmC,CAGnC,oBAAqB,CADrB,iBAEF,CAEA,YAEE,0BAAwC,CAExC,wBAAyB,CACzB,iBAAkB,CAFlB,aAAc,CAFd,8BAAkC,CAMlC,kBAAmB,CADnB,cAAgB,CAEhB,iBACF,CAEA,WAGE,QACF,CAEA,uBALE,YAAa,CACb,qBAQF,CAJA,YAGE,SACF,CAEA,kBAEE,UAAY,CADZ,+BAAmC,CAEnC,eACF,CAEA,kBAIE,0BAA0C,CAD1C,0BAA0C,CAD1C,iBAAkB,CAGlB,UAAY,CACZ,cAAe,CALf,cAMF,CAEA,wBAEE,oBAAqB,CACrB,8BAA6C,CAF7C,YAGF,CAEA,aAGE,qBAAyB,CAEzB,WAAY,CACZ,iBAAkB,CAFlB,UAAY,CAKZ,cAAe,CARf,+BAAmC,CAMnC,cAAe,CACf,eAAiB,CAGjB,gBAAkB,CATlB,cAAgB,CAQhB,+BAEF,CAEA,mBACE,wBACF,CAEA,sBACE,wBAAyB,CACzB,kBACF,CAEA,aACE,iBAAkB,CAClB,iBACF,CAEA,eAEE,eAAgB,CAChB,WAAY,CACZ,UAAc,CAEd,cAAe,CALf,+BAAmC,CAInC,eAAiB,CAEjB,yBACF,CAEA,qBACE,aACF,CAEA,wBACE,aAAc,CACd,kBACF,CAGA,qBAEE,eAAgB,CAChB,YAAa,CAFb,UAGF,CAEA,kBAGE,kBAAmB,CAFnB,YAAa,CACb,6BAA8B,CAE9B,kBACF,CAEA,qBAGE,gBAAiB,CACjB,QACF,CAEA,oCALE,UAAY,CADZ,+BAgBF,CAVA,eAEE,0BAAwC,CAExC,0BAAwC,CACxC,iBAAkB,CAGlB,cAAe,CADf,eAAiB,CADjB,kBAAoB,CAGpB,+BACF,CAEA,qBACE,0BACF,CAEA,mBACE,YAAa,CACb,QAAS,CACT,kBACF,CAEA,eAGE,0BAA0C,CAE1C,WAAY,CACZ,iBAAkB,CAFlB,UAAY,CAKZ,cAAe,CARf,QAAO,CAMP,cAAe,CACf,eAAiB,CANjB,YAAa,CAQb,+BACF,CAEA,qBACE,sBACF,CAGA,wBAGE,kBAAmB,CAGnB,0BAA0C,CAC1C,iBAAkB,CAElB,qBAAsB,CARtB,YAAa,CACb,qBAAsB,CAEtB,UAAW,CACX,YAAa,CAGb,UAEF,CAEA,2BAEE,UAAY,CADZ,+BAAmC,CAEnC,gBAAiB,CACjB,QAAS,CACT,iBACF,CAEA,sBAKE,WAAY,CACZ,iBAAkB,CAClB,cAAe,CANf,+BAAmC,CAEnC,gBAAiB,CACjB,eAAiB,CAMjB,kBAAmB,CARnB,iBAAkB,CAOlB,wBAAyB,CADzB,uBAGF,CAEA,8BACE,qBAAyB,CAEzB,+BAA4C,CAD5C,UAEF,CAEA,oCACE,wBAAyB,CAEzB,2BAA4C,CAD5C,0BAEF,CASA,8DACE,0BAA0C,CAG1C,eAAgB,CAFhB,eAA+B,CAC/B,kBAEF,CAGA,mBAEE,YAAa,CACb,sBAAuB,CAFvB,UAGF,CAEA,oBAEE,sBAAuB,CAEvB,UAAY,CAGZ,cAAe,CANf,YAAa,CAIb,cAAe,CAFf,UAAY,CAGZ,eAAgB,CAEhB,eAAgB,CAChB,eACF,CAEA,yCAKE,oBAAqB,CADrB,cAAe,CAEf,aAAc,CAJd,WAAY,CAKZ,cAAe,CANf,UAOF,CAEA,sBACE,aAAc,CAEd,eAAiB,CADjB,oBAAqB,CAErB,yBACF,CAEA,4BACE,aAAc,CACd,yBACF,CAEA,eACE,0BAA0C,CAC1C,iBAAkB,CAElB,eAAgB,CADhB,cAEF,CAEA,kBACE,UAAY,CAGZ,gBAAiB,CADjB,kBAAmB,CADnB,YAGF,CAEA,YACE,YAAa,CACb,qBAAsB,CACtB,UACF,CAEA,WAGE,kBAAmB,CAEnB,sBAAoC,CACpC,iBAAkB,CALlB,YAAa,CACb,6BAA8B,CAE9B,cAGF,CAEA,WACE,UAEF,CAEA,mBACE,sBAAuC,CAEvC,WAAY,CACZ,iBAAkB,CAFlB,UAAY,CAKZ,cAAe,CADf,eAAiB,CADjB,kBAAoB,CAGpB,+BACF,CAEA,yBACE,0BACF,CAEA,kBACE,eAA+B,CAE/B,YAAa,CADb,iBAEF,CAGA,wBAME,kBAAmB,CAFnB,YAAa,CACb,qBAAsB,CAHtB,eAAgB,CAChB,YAAa,CAFb,UAMF,CAEA,qBAGE,kBAAmB,CAFnB,YAAa,CACb,6BAA8B,CAE9B,kBACF,CAEA,wBAEE,WAA+B,CAD/B,+BAAmC,CAEnC,gBAAiB,CACjB,QACF,CAGA,kBAEE,UAAY,CADZ,yBAEF,CAEA,aAEE,sBAAuC,CAEvC,WAAY,CACZ,iBAAkB,CAFlB,UAAY,CAKZ,cAAe,CAGf,aAAc,CAVd,+BAAmC,CAMnC,cAAe,CAGf,kBAAmB,CAJnB,qBAAuB,CAGvB,+BAAiC,CAGjC,yBAAkB,CAAlB,iBACF,CAEA,mBACE,0BACF,CAGA,wBACE,cACF,CAEA,8BAEE,eAAgB,CADhB,cAEF,CAGA,YACE,kBAIF,CAEA,wBAJE,YAAa,CACb,sBAAuB,CAFvB,UAaF,CARA,YAEE,kBAAmB,CAEnB,QAAS,CAET,eAAgB,CADhB,kBAGF,CAEA,gBACE,aACF,CAEA,YACE,WAAY,CACZ,UACF,CAEA,mBACE,QACF,CAEA,qBAGE,WAA+B,CAF/B,8BAAkC,CAClC,gBAAiB,CAEjB,QACF,CAGA,iBACE,kBAAmB,CACnB,UACF,CAEA,eAKE,0BAAqC,CACrC,kBAAmB,CALnB,YAAa,CAEb,cAAe,CADf,sBAAuB,CAMvB,aAAc,CADd,eAAgB,CAHhB,mBAKF,CAEA,UAEE,UAAY,CADZ,+BAAmC,CAInC,eAAgB,CADhB,qBAAuB,CAIvB,iBAAkB,CALlB,oBAAqB,CAGrB,8BAAgC,CAChC,kBAEF,CAEA,iCAQE,0BAA0C,CAP1C,UAAW,CAMX,UAAW,CALX,iBAAkB,CAClB,OAAQ,CACR,OAAQ,CACR,0BAA2B,CAC3B,SAGF,CAEA,gBACE,UAAc,CACd,0BACF,CAEA,iBACE,UAAc,CACd,eACF,CAGA,aAGE,yBAAuB,CAMvB,gBAAiB,CAJjB,0BAA0C,CAC1C,mBAAqB,CACrB,8DAAmF,CANnF,YAAa,CAEb,oBAAuB,CADvB,mCAAqC,CAQrC,eAAgB,CAChB,cAAe,CAPf,sBAAyB,CAIzB,mBAIF,CAGA,cAcE,gBAAiB,CAXjB,0BAA6B,CAM7B,eAAgB,CAChB,gCAA2C,CAR3C,UAAW,CAaX,WAAY,CAFZ,SAAU,CALV,8BAAgC,CAPhC,SAeF,CAIA,mCAfE,kBAAmB,CAEnB,cAAe,CAHf,YAAa,CAEb,sBAAuB,CAKvB,eAAgB,CAChB,iBAiBF,CAEA,0CATE,WAAY,CADZ,UAkBF,CARA,qBAME,eAAgB,CADhB,cAAe,CAFf,gBAAiB,CACjB,sBAAuB,CAGvB,6BACF,CAEA,gDACE,qBACF,CAEA,iDACE,oBACF,CAGA,cAEE,UAAW,CAGX,YAAa,CAEb,OAAQ,CADR,sBAAuB,CAHvB,MAAO,CAMP,mBAAoB,CARpB,iBAAkB,CAGlB,UAAW,CAIX,SAEF,CAEA,MAEE,0CAA+C,CAD/C,cAEF,CAIA,qBACE,8DAAmF,CACnF,iBACF,CAGA,4BAOE,0BAAqC,CANrC,UAAW,CAKX,WAAY,CAFZ,MAAO,CAKP,mBAAoB,CAPpB,iBAAkB,CAClB,KAAM,CAEN,UAAW,CAGX,SAEF,CAEA,2BAME,eAA+B,CAL/B,iBAAkB,CAMlB,yBAA2B,CAC3B,eAAiB,CAJjB,QAAS,CAMT,mBAAoB,CARpB,iBAAkB,CASlB,iBAAkB,CARlB,OAAQ,CAER,8BAAgC,CAOhC,UAAW,CAHX,SAIF,CAGA,gCACE,cAAe,CACf,yBACF,CAGA,2BAEE,kBAAmB,CADnB,YAAa,CAGb,UAAW,CADX,wBAAyB,CAEzB,0BAA4B,CAE5B,iBAAkB,CADlB,oBAEF,CAGA,kBAEE,kBAAmB,CADnB,YAAa,CAEb,QACF,CAGA,gBAaE,kBAAmB,CATnB,0BAA0C,CAC1C,mBAAqB,CAIrB,8DAAmF,CAFnF,qBAAsB,CADtB,UAAY,CAIZ,YAAa,CACb,qBAAsB,CATtB,YAAa,CAUb,sBAAuB,CATvB,sBAAyB,CAKzB,iBAAkB,CAPlB,WAaF,CAEA,aACE,eAAiB,CAEjB,kBAAmB,CAEnB,mBAAqB,CADrB,UAAY,CAFZ,wBAIF,CAEA,6BAGE,UAAY,CAFZ,gBAAiB,CACjB,eAAiB,CAGjB,iBAAkB,CADlB,2BAEF,CAGA,wBAEE,MAAO,CADP,iBAAkB,CAGlB,wBAGF,CAEA,sCAHE,kBAAmB,CADnB,YAAa,CAFb,YAiBF,CAXA,cAGE,0BAA0C,CAC1C,kBAAmB,CAKnB,sBAAuB,CAHvB,WAAY,CACZ,eAAgB,CAFhB,iBAAkB,CAJlB,UAUF,CAEA,mBAEE,eAAiB,CACjB,kBAAmB,CAFnB,WAAY,CAMZ,MAAO,CAFP,iBAAkB,CAClB,KAAM,CAFN,6BAA+B,CAI/B,SACF,CAEA,eAQE,eAA+B,CAK/B,sCAAyC,CAZzC,gBAAiB,CACjB,eAAgB,CAEhB,kBAAmB,CAUnB,aAAc,CAPd,yBAA0B,CAG1B,mBAAoB,CALpB,iBAAkB,CAIlB,iBAAkB,CAGlB,+BAAyC,CATzC,wBAAyB,CAQzB,UAAW,CALX,SASF,CAGA,kBAKE,YAAa,CAFb,0BAIF,CAEA,2CAPE,QAAS,CADT,iBAAkB,CAGlB,SAAU,CAEV,UAaF,CAVA,yBAOE,qBAAuB,CANvB,UAAW,CAKX,WAAY,CAFZ,MAAO,CAKP,mBACF,CAEA,iBAEE,YAAa,CAGb,UAAY,CACZ,eAAiB,CAHjB,QAAS,CAMT,mBAAoB,CARpB,iBAAkB,CAUlB,iBAAkB,CAPlB,0BAA2B,CAG3B,kBAAmB,CAGnB,yBAAkB,CAAlB,iBAAkB,CAFlB,UAIF,CAGA,qBAGE,UAAY,CAFZ,eAAgB,CAChB,iBAEF,CAEA,mBAKE,WAA+B,CAJ/B,+BAAmC,CACnC,cAAe,CACf,eAAiB,CACjB,mBAEF,CAEA,kBAIE,aAAc,CAHd,8BAAkC,CAClC,gBAAiB,CACjB,eAAiB,CAEjB,+BACF,CAGA,gBAUE,kBAAmB,CANnB,0BAA0C,CAC1C,WAAY,CAFZ,iBAAkB,CAUlB,8BAAwC,CAPxC,UAAY,CAKZ,cAAe,CAHf,YAAa,CADb,cAAe,CALf,WAAY,CAOZ,sBAAuB,CAGvB,uBAAyB,CAXzB,UAaF,CAEA,sBACE,sBAA0C,CAC1C,uBACF,CAEA,uBACE,kCACF,CAGA,sBASE,kBAAmB,CAEnB,iCAA0B,CAA1B,yBAA0B,CAL1B,0BAAoC,CACpC,YAAa,CAFb,WAAY,CAGZ,sBAAuB,CALvB,MAAO,CAFP,cAAe,CACf,KAAM,CAEN,UAAW,CAMX,YAEF,CAEA,qBAQE,mCAAqC,CAPrC,wBAAyB,CACzB,iBAAkB,CAGlB,gCAA0C,CAE1C,UAAY,CAJZ,cAAe,CAGf,iBAAkB,CAFlB,oBAKF,CAEA,wBACE,GACE,SAAU,CACV,2BACF,CACA,GACE,SAAU,CACV,uBACF,CACF,CAEA,wBAEE,aAAc,CACd,gBAAiB,CAFjB,YAGF,CAEA,uBAEE,eAAgB,CADhB,oBAEF,CAEA,sBACE,YAAa,CAEb,QAAS,CADT,sBAEF,CAEA,6BAEE,WAAY,CACZ,iBAAkB,CAElB,cAAe,CADf,eAAiB,CAHjB,qBAAuB,CAKvB,+BACF,CAEA,eACE,wBAAyB,CACzB,UACF,CAEA,qBACE,wBACF,CAEA,gBACE,wBAAyB,CACzB,UACF,CAEA,sBACE,wBACF,CAEA,yBACE,wBAAyB,CACzB,kBACF,CAEA,+BACE,wBACF,CAGA,iBAQE,mCAAqC,CAPrC,wBAAyB,CACzB,iBAAkB,CAGlB,gCAA0C,CAE1C,UAAY,CAJZ,YAAa,CAGb,iBAAkB,CAFlB,oBAKF,CAEA,oBAEE,aAAc,CACd,gBAAiB,CACjB,oBAAqB,CAHrB,YAIF,CAEA,gBACE,kBAAmB,CACnB,eACF,CAEA,kBAGE,eAA+B,CAC/B,cAAe,CAFf,eAAgB,CADhB,QAIF,CAEA,kBACE,aAAc,CAEd,eAAiB,CADjB,oBAAqB,CAErB,yBACF,CAEA,wBACE,aAAc,CACd,yBACF,CAEA,4BAEE,YAAa,CACb,sBAAuB,CAFvB,kBAGF,CAEA,qCAEE,kBAAmB,CAEnB,UAAY,CAGZ,cAAe,CANf,YAAa,CAIb,gBAAiB,CACjB,eAAiB,CAHjB,UAAY,CAKZ,eACF,CAEA,0DAKE,oBAAqB,CADrB,cAAe,CAEf,aAAc,CAJd,WAAY,CACZ,QAAS,CAFT,UAMF,CAGA,qBAGE,0BAA0C,CAC1C,mBAAqB,CAGrB,qBAAsB,CADtB,UAAY,CALZ,0BAA4B,CAC5B,uBAAyB,CAGzB,oBAGF,CAEA,wBAIE,iCAAiD,CADjD,UAAY,CAFZ,gBAAiB,CACjB,kBAAmB,CAGnB,oBACF,CAEA,uBACE,YAAa,CACb,qBAAsB,CACtB,QACF,CAEA,mBAEE,kBAAmB,CAGnB,sBAAoC,CACpC,mBAAqB,CALrB,YAAa,CAEb,QAAS,CACT,aAAe,CAGf,6BACF,CAEA,yBAEE,0BAAoC,CADpC,yBAEF,CAEA,WAIE,kBAAmB,CAEnB,0BAA0C,CAC1C,iBAAkB,CAJlB,YAAa,CAMb,gBAAiB,CADjB,eAAiB,CANjB,WAAY,CAGZ,sBAAuB,CAJvB,UASF,CAEA,WAGE,gBAAiB,CADjB,eAAiB,CAEjB,eAAgB,CAChB,sBAAuB,CACvB,kBAAmB,CALnB,UAMF,CAEA,qBAQE,kBAAmB,CALnB,0BAAoC,CACpC,kBAAmB,CAKnB,cAAe,CAFf,YAAa,CANb,QAAO,CACP,WAAY,CAIZ,eAAgB,CADhB,iBAAkB,CAKlB,oCACF,CAEA,2BACE,sBACF,CAEA,WAEE,eAAiB,CACjB,kBAAmB,CAFnB,WAAY,CAGZ,6BACF,CAEA,cAKE,UAAY,CACZ,aAAc,CAFd,gBAAiB,CADjB,eAAiB,CADjB,gBAAiB,CADjB,UAMF,CAGA,eAGE,0BAA0C,CAC1C,mBAAqB,CAGrB,qBAAsB,CADtB,UAAY,CALZ,0BAA4B,CAC5B,uBAAyB,CAGzB,oBAGF,CAEA,kBAIE,iCAAiD,CADjD,UAAY,CAFZ,gBAAiB,CACjB,kBAAmB,CAGnB,oBACF,CAEA,iBACE,cAAe,CACf,eACF,CAGA,yBAQE,8BAA8C,CAF9C,kBAAmB,CADnB,eAAgB,CAEhB,gBAEF,CAEA,0CAPE,kBAAmB,CAFnB,YAAa,CACb,qBAAsB,CAFtB,UAeF,CAGA,cACE,eAAiB,CACjB,iBACF,CAEA,gBACE,aAAc,CAEd,cAAgB,CADhB,oBAAqB,CAErB,yBACF,CAEA,sBACE,UAAY,CACZ,yBACF,CAGA,uBAEE,sBAKF,CAGA,8CAPE,kBAAmB,CAFnB,YAAa,CAGb,eAAgB,CAEhB,cAAe,CADf,UAaF,CARA,uBAOE,UAAY,CALZ,qBAMF,CAEA,oBAEE,kBAAmB,CACnB,eAAgB,CAFhB,iBAGF,CAEA,uBAIE,aAAc,CAHd,+BAAmC,CACnC,cAAe,CAGf,eAAiB,CAFjB,mBAAqB,CAGrB,iCACF,CAEA,kBAGE,eAA+B,CAF/B,8BAAkC,CAClC,gBAAiB,CAGjB,iBAAkB,CADlB,oBAEF,CAEA,cACE,YAAa,CAGb,cAAe,CADf,QAAS,CADT,sBAAuB,CAGvB,eACF,CAEA,mBAEE,0BAAyC,CAIzC,0BAAyC,CAFzC,kBAAmB,CAHnB,+BAAmC,CAInC,eAAiB,CAFjB,kBAIF,CAEA,qBAEE,qBAAsB,CADtB,qBAEF,CAGA,eACE,0BAA0C,CAI1C,6BAA8B,CAF9B,kBAAmB,CACnB,kBAAmB,CAFnB,YAIF,CAEA,iBAKE,eAA+B,CAJ/B,8BAAkC,CAClC,gBAAiB,CACjB,eAAgB,CAChB,QAEF,CAGA,eACE,YAAa,CACb,qBAAsB,CACtB,QACF,CAEA,cACE,0BAA2C,CAI3C,0BAA0C,CAH1C,oBAAqB,CAErB,+BAAyC,CADzC,cAAe,CAGf,iDACF,CAEA,oBAEE,gCAA+C,CAD/C,0BAEF,CAEA,aAIE,QAAS,CADT,oBAEF,CAEA,0BALE,kBAAmB,CADnB,YAoBF,CAdA,aAEE,kDAAqD,CAIrD,iBAAkB,CAMlB,+BAA8C,CAT9C,UAAY,CAUZ,aAAc,CAHd,gBAAiB,CALjB,WAAY,CAIZ,sBAAuB,CALvB,UAUF,CAEA,yBAfE,+BAAmC,CAUnC,eAWF,CANA,YAGE,aAAc,CADd,gBAAiB,CAEjB,QAEF,CAEA,WAEE,aAAS,CACT,kBAAmB,CAFnB,YAAa,CACb,QAEF,CAGA,8BACE,+BACF,CAEA,+BACE,+BACF,CAEA,+BACE,OACF,CAEA,gCACE,OACF,CAEA,aAIE,eAA+B,CAH/B,8BAAkC,CAClC,gBAAiB,CACjB,eAAgB,CAEhB,QACF,CAEA,YAGE,kBAAmB,CAFnB,YAAa,CACb,sBAEF,CAEA,eAKE,0BAAyC,CAFzC,kBAAmB,CACnB,2BAAyC,CAFzC,WAAY,CADZ,cAAe,CAKf,iDACF,CAEA,qBAEE,gCAA+C,CAD/C,qBAEF,CAGA,oBACE,sDAAoF,CAKpF,0BAAwC,CAHxC,oBAAqB,CACrB,eAAgB,CAFhB,cAAe,CAGf,iBAEF,CAEA,uBAGE,aAAc,CAFd,+BAAmC,CACnC,cAAe,CAGf,eAAiB,CADjB,kBAEF,CAEA,sBAIE,eAA+B,CAH/B,8BAAkC,CAClC,gBAAiB,CACjB,eAAgB,CAEhB,QACF,CAGA,yBACE,uBACE,gBACF,CAEA,kBACE,gBACF,CAEA,cACE,QACF,CAEA,mBACE,+BAAmC,CACnC,eAAiB,CACjB,mBACF,CAEA,cACE,cACF,CAEA,aAGE,gBAAiB,CADjB,aAAc,CADd,YAGF,CAEA,YACE,gBACF,CAGA,6DAGE,UAAW,CADX,yBAEF,CAEA,+DAEE,OACF,CAEA,aACE,cACF,CAEA,eACE,cACF,CAEA,iBACE,cACF,CAEA,oBACE,cACF,CAEA,uBACE,gBACF,CAEA,sBACE,cACF,CACF,CAGA,iBAIE,UAAY,CAFZ,gBAAiB,CACjB,YAAa,CAFb,UAIF,CAEA,oBAGE,aAAc,CAFd,+BAAmC,CAGnC,gBAAiB,CACjB,kBAAmB,CAHnB,iBAAkB,CAIlB,iCACF,CAEA,eACE,UACF,CAEA,gBACE,kBACF,CAEA,cACE,+BAAmC,CACnC,gBAAiB,CACjB,eAAiB,CACjB,kBAAmB,CACnB,iBAAkB,CAClB,iCACF,CAEA,YAGE,eAAW,CAFX,YAAa,CAEb,UAAW,CADX,yDAA4D,CAE5D,cACF,CAEA,WACE,0BAA0C,CAM1C,sBAA6B,CAL7B,kBAAmB,CAGnB,cAAe,CAFf,YAAa,CACb,iBAAkB,CAElB,uBAEF,CAEA,iBAEE,0BAA2C,CAC3C,sBAAqC,CACrC,+BAA8C,CAH9C,0BAIF,CAEA,sBAEE,oBAAsB,CADtB,iBAEF,CAEA,gBAIE,iBAAkB,CAFlB,YAAa,CACb,gBAAiB,CAFjB,UAIF,CAEA,YAKE,WAAY,CAFZ,MAAO,CAGP,gBAAiB,CACjB,mBAAoB,CANpB,iBAAkB,CAClB,KAAM,CAEN,UAIF,CAEA,WACE,iBACF,CAEA,WAIE,UAAY,CAHZ,+BAAmC,CACnC,gBAAiB,CACjB,eAAiB,CAEjB,iBACF,CAEA,aAGE,eAA+B,CAF/B,8BAAkC,CAClC,eAAiB,CAEjB,QACF,CAEA,gCAIE,WAA+B,CAH/B,8BAAkC,CAElC,gBAAiB,CAEjB,aACF,CAOA,YAGE,WAA+B,CAC/B,iBAAmB,CAHnB,eAAgB,CAChB,iBAGF,CAGA,kBASE,kBAAmB,CAEnB,+BAA0B,CAA1B,uBAA0B,CAL1B,sBAAkC,CAClC,YAAa,CAFb,WAAY,CAGZ,sBAAuB,CALvB,MAAO,CAFP,cAAe,CACf,KAAM,CAUN,qEAAiE,CAAjE,6DAAiE,CAAjE,8FAAiE,CARjE,UAAW,CAMX,YAGF,CAEA,0BAEE,iCAA0B,CAA1B,yBAA0B,CAD1B,0BAEF,CAEA,oBAME,kBAAmB,CAFnB,YAAa,CACb,qBAAsB,CAFtB,eAAgB,CADhB,aAAc,CAMd,SAAU,CAPV,iBAAkB,CAMlB,mBAAqB,CAErB,2EACF,CAEA,4BAEE,SAAU,CADV,kBAEF,CAEA,0BAEE,oBAAqB,CADrB,iBAEF,CAEA,oBAEE,eAKF,CAEA,4CALE,iBAAkB,CAClB,gCAA0C,CAC1C,aAAc,CALd,cAAe,CAEf,kBAcF,CARA,wBAEE,WAMF,CAEA,wBAKE,WAAY,CAFZ,MAAO,CAGP,kBAAmB,CACnB,mBAAoB,CANpB,iBAAkB,CAClB,KAAM,CAEN,UAAW,CAIX,SACF,CAEA,cAaE,kBAAmB,CAPnB,sBAA0C,CAC1C,WAAY,CACZ,iBAAkB,CAClB,UAAY,CAKZ,cAAe,CAHf,YAAa,CADb,cAAe,CALf,WAAY,CAOZ,sBAAuB,CAXvB,iBAAkB,CAElB,WAAY,CADZ,SAAU,CAaV,+BAAiC,CAXjC,UAAW,CAYX,YACF,CAEA,oBACE,sBACF,CAEA,kBACE,YAAa,CACb,QAAS,CACT,eAAgB,CAChB,UACF,CAEA,cAGE,wBAA6B,CAE7B,qBAAuB,CACvB,iBAAkB,CAKlB,eAAgB,CAPhB,UAAY,CAKZ,cAAe,CAIf,QAAO,CAZP,+BAAmC,CAMnC,cAAe,CACf,eAAiB,CANjB,iBAAkB,CAUlB,iBAAkB,CAFlB,kBAIF,CAEA,oBACE,0BAA0C,CAC1C,kBACF,CAGA,sBACE,wBAA6B,CAE7B,oBAAqB,CADrB,aAEF,CAEA,4BACE,0BAAwC,CACxC,sBACF,CAEA,gBAGE,wBAAyB,CAEzB,WAAY,CACZ,iBAAkB,CAFlB,UAAY,CAKZ,cAAe,CAGf,QAAO,CAXP,+BAAmC,CAMnC,cAAe,CACf,eAAiB,CANjB,iBAAkB,CASlB,iBAAkB,CADlB,kBAIF,CAEA,sBAEE,sBAAuB,CADvB,0BAEF,CAEA,2BASE,kBAAmB,CAHnB,0BAAqC,CAOrC,cAAe,CAVf,MAAO,CASP,aAAc,CAXd,iBAAkB,CAClB,KAAM,CAQN,UAIF,CAEA,sDALE,iBAAkB,CAJlB,YAAa,CAFb,WAAY,CAGZ,sBAAuB,CAJvB,UAyBF,CAbA,2BAME,0BAAoC,CAEpC,8BAAwC,CAPxC,UAAY,CAQZ,cAAe,CAEf,qBAAsB,CANtB,eAAgB,CAHhB,YAWF,CAEA,6BACE,8BAAkC,CAGlC,eAAgB,CADhB,eAAgB,CADhB,iBAGF,CAEA,oCAEE,aAAc,CAEd,aAAc,CAHd,+BAAmC,CAEnC,eAAgB,CAEhB,iBACF,CAGA,wBACE,iBACF,CAEA,cACE,UAAW,CAGX,+BAAmC,CADnC,eAAgB,CADhB,eAGF,CAEA,oBACE,WAA+B,CAG/B,8BAAkC,CADlC,eAAgB,CAEhB,eAAgB,CAHhB,eAIF,CAEA,gBAIE,kBAAmB,CAHnB,YAAa,CACb,qBAAsB,CACtB,QAEF,CAEA,mEAOE,WAAY,CACZ,iBAAkB,CAGlB,UAAY,CAFZ,cAAe,CAHf,+BAAmC,CADnC,aAAc,CAFd,eAAgB,CAChB,iBAAkB,CAMlB,uBAEF,CAEA,kIAIE,sBAAuB,CADvB,0BAEF,CAEA,8FAIE,kBAAmB,CADnB,UAAY,CAEZ,cACF,CAEA,oBAIE,kBAAmB,CAHnB,YAAa,CACb,qBAAsB,CACtB,QAAS,CAET,UACF,CAEA,sBACE,8BAAkC,CAGlC,eAAgB,CADhB,eAAgB,CADhB,iBAGF,CAEA,6BAGE,aAAc,CAFd,+BAAmC,CACnC,eAAgB,CAEhB,iBACF,CAEA,gBACE,YAAa,CAEb,cAAe,CADf,QAAS,CAET,sBACF,CAGA,yBACE,oBACE,aAAc,CACd,mBACF,CAEA,4BACE,kBACF,CAEA,cAKE,cAAe,CADf,WAAY,CAFZ,WAAY,CADZ,SAAU,CAEV,UAGF,CAEA,kBACE,kBAAmB,CACnB,OAAQ,CACR,UACF,CAEA,8BAEE,cAAe,CADf,gBAEF,CAEA,cACE,gBAAiB,CACjB,QACF,CAEA,gBACE,QAAO,CACP,gBACF,CAEA,2BAEE,cAAe,CADf,YAEF,CAEA,6BAEE,eAAgB,CADhB,iBAEF,CAEA,oCACE,eAAgB,CAChB,iBACF,CAGA,kBACE,uEAAmE,CAAnE,+DAAmE,CAAnE,iGACF,CAEA,oBACE,6EACF,CAGA,cACE,eACF,CAEA,oBACE,aAAc,CACd,kBACF,CAEA,gBACE,QACF,CAEA,gBACE,qBAAsB,CACtB,QACF,CAEA,mEAIE,cAAgB,CAChB,eAAgB,CAFhB,iBAGF,CACF,CAGA,gCAIE,mBAAqB,CADrB,WAAY,CADZ,YAAa,CADb,iBAIF,CAEA,iBACE,0BAA0C,CAC1C,UACF,CAEA,eACE,0BAAwC,CAExC,wBAAyB,CADzB,aAEF,CAIA,yBACE,aACE,SAAW,CACX,aACF,CAEA,cAEE,UAAW,CADX,SAEF,CAEA,2BAEE,eAA+B,CAD/B,gBAEF,CAEA,gCACE,gBACF,CAGA,cACE,UAAW,CACX,OACF,CAEA,MACE,cACF,CACF,CAGA,8BACE,YAEE,kBAAmB,CADnB,qBAAsB,CAEtB,SAAW,CACX,iBACF,CAEA,YACE,WACF,CAEA,qBACE,cACF,CAEA,eACE,oBACF,CAEA,UAEE,eAAiB,CADjB,kBAEF,CAEA,YAEE,QAAS,CADT,yDAEF,CAEA,gBACE,YACF,CAEA,2BAEE,SAAW,CADX,iBAEF,CAEA,kBACE,SACF,CAEA,gBAEE,WAAY,CACZ,aAAe,CAFf,UAGF,CAEA,aACE,eACF,CAEA,6BACE,gBACF,CAEA,gBAGE,cAAe,CADf,WAAY,CADZ,UAGF,CAEA,wBACE,WAAY,CACZ,wBACF,CAEA,cACE,WACF,CAEA,eACE,cACF,CAEA,kBACE,QAAS,CACT,WACF,CAEA,yBACE,WACF,CAEA,iBAEE,YAAa,CADb,eAEF,CAEA,oCACE,iBAAkB,CAClB,YACF,CAEA,mBAEE,SAAW,CADX,aAEF,CAEA,WAGE,cAAe,CADf,aAAc,CADd,YAGF,CAEA,WAEE,cAAe,CADf,UAEF,CAEA,qBACE,aACF,CAEA,cAEE,cAAe,CADf,YAEF,CACF,CAGA,sDACE,eACE,aACF,CAEA,YACE,mBACF,CAEA,iBACE,kBACF,CAEA,YACE,WACF,CAEA,qBACE,eACF,CAEA,eACE,oBACF,CAEA,UAEE,eAAiB,CADjB,mBAEF,CAEA,aACE,eACF,CAEA,2BAEE,SAAW,CADX,eAEF,CAEA,kBACE,SACF,CAEA,gBAEE,WAAY,CACZ,aAAe,CAFf,UAGF,CAEA,aACE,eAAiB,CACjB,mBACF,CAEA,6BACE,gBACF,CAEA,gBAGE,cAAe,CADf,WAAY,CADZ,UAGF,CAEA,wBACE,WAAY,CACZ,wBACF,CAEA,cACE,WACF,CAEA,eACE,gBACF,CAEA,kBACE,QAAS,CACT,WACF,CAEA,yBACE,WACF,CAEA,iBAEE,YAAa,CADb,gBAEF,CAEA,oCACE,eAAgB,CAChB,aACF,CAEA,uBACE,SACF,CAEA,mBAEE,SAAW,CADX,aAEF,CAEA,WAGE,eAAiB,CADjB,aAAc,CADd,YAGF,CAEA,WAEE,eAAiB,CADjB,UAEF,CAEA,qBACE,aACF,CAEA,cAEE,eAAiB,CADjB,UAEF,CACF,CAGA,gDACE,aACE,oBAAuB,CACvB,wBACF,CAEA,cAGE,0BAA6B,CAD7B,UAAW,CADX,SAGF,CAEA,2BAEE,eAA+B,CAD/B,cAEF,CAEA,gCACE,gBACF,CAGA,cACE,UAAW,CACX,OACF,CAEA,MACE,cACF,CACF,CAGA,0BACE,aAGE,SAAW,CADX,eAAgB,CADhB,cAGF,CAEA,cAGE,gBAAiB,CADjB,UAAW,CADX,SAGF,CAEA,2BAEE,eAA+B,CAD/B,gBAEF,CAEA,gCACE,gBACF,CAGA,cACE,WAAY,CACZ,OACF,CAEA,MAEE,0CAA+C,CAD/C,cAEF,CACF", "sources": ["index.css", "App.css"], "sourcesContent": ["body {\n  margin: 0;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',\n    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\n    sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\n\ncode {\n  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',\n    monospace;\n}\n", "/* Font Face Declarations */\r\n@font-face {\r\n  font-family: 'Absender';\r\n  src: url('./fonts/TT Norms Pro Serif Trial DemiBold.ttf') format('truetype');\r\n  font-weight: normal;\r\n  font-style: normal;\r\n}\r\n\r\n@font-face {\r\n  font-family: 'Vividly';\r\n  src: url('./fonts/TT Norms Pro Serif Trial Light.ttf') format('truetype');\r\n  font-weight: normal;\r\n  font-style: normal;\r\n}\r\n\r\n/* App Container */\r\n.app-container {\r\n  min-height: 100vh;\r\n  width: 100%;\r\n  background: linear-gradient(to bottom right, #1a1a1a, #000000);\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: flex-start; /* Start from the top to ensure content is visible */\r\n  padding: min(5vw, 1rem); /* Responsive padding */\r\n  box-sizing: border-box; /* Include padding in width calculation */\r\n  font-family: 'Vividly', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;\r\n  overflow-x: hidden; /* Prevent horizontal scrolling */\r\n}\r\n\r\n/* Authentication Styles */\r\n.auth-container {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  min-height: 60vh;\r\n  width: 100%;\r\n  padding: 2rem 0;\r\n}\r\n\r\n.auth-form-container {\r\n  background-color: rgba(255, 255, 255, 0.1);\r\n  border-radius: 8px;\r\n  padding: 2rem;\r\n  width: 100%;\r\n  max-width: 400px;\r\n  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.auth-form-container h2 {\r\n  font-family: 'Absender', sans-serif;\r\n  color: white;\r\n  text-align: center;\r\n  margin-bottom: 1.5rem;\r\n}\r\n\r\n.auth-error {\r\n  font-family: 'Vividly', sans-serif;\r\n  background-color: rgba(244, 67, 54, 0.1);\r\n  color: #F44336;\r\n  border: 1px solid #F44336;\r\n  border-radius: 4px;\r\n  padding: 0.75rem;\r\n  margin-bottom: 1rem;\r\n  text-align: center;\r\n}\r\n\r\n.auth-form {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 1rem;\r\n}\r\n\r\n.form-group {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 0.5rem;\r\n}\r\n\r\n.form-group label {\r\n  font-family: 'Absender', sans-serif;\r\n  color: white;\r\n  font-weight: bold;\r\n}\r\n\r\n.form-group input {\r\n  padding: 0.75rem;\r\n  border-radius: 4px;\r\n  border: 1px solid rgba(255, 255, 255, 0.3);\r\n  background-color: rgba(255, 255, 255, 0.1);\r\n  color: white;\r\n  font-size: 1rem;\r\n}\r\n\r\n.form-group input:focus {\r\n  outline: none;\r\n  border-color: #4fc3f7;\r\n  box-shadow: 0 0 0 2px rgba(79, 195, 247, 0.3);\r\n}\r\n\r\n.auth-button {\r\n  font-family: 'Absender', sans-serif;\r\n  padding: 0.75rem;\r\n  background-color: #cc5500;\r\n  color: white;\r\n  border: none;\r\n  border-radius: 4px;\r\n  font-size: 1rem;\r\n  font-weight: bold;\r\n  cursor: pointer;\r\n  transition: background-color 0.2s;\r\n  margin-top: 0.5rem;\r\n}\r\n\r\n.auth-button:hover {\r\n  background-color: #b84700;\r\n}\r\n\r\n.auth-button:disabled {\r\n  background-color: #b0bec5;\r\n  cursor: not-allowed;\r\n}\r\n\r\n.auth-toggle {\r\n  margin-top: 1.5rem;\r\n  text-align: center;\r\n}\r\n\r\n.toggle-button {\r\n  font-family: 'Absender', sans-serif;\r\n  background: none;\r\n  border: none;\r\n  color: #cc5500;\r\n  font-size: 0.9rem;\r\n  cursor: pointer;\r\n  text-decoration: underline;\r\n}\r\n\r\n.toggle-button:hover {\r\n  color: #b84700;\r\n}\r\n\r\n.toggle-button:disabled {\r\n  color: #b0bec5;\r\n  cursor: not-allowed;\r\n}\r\n\r\n/* Dashboard Styles */\r\n.dashboard-container {\r\n  width: 100%;\r\n  max-width: 800px;\r\n  padding: 1rem;\r\n}\r\n\r\n.dashboard-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 2rem;\r\n}\r\n\r\n.dashboard-header h1 {\r\n  font-family: 'Absender', sans-serif;\r\n  color: white;\r\n  font-size: 1.8rem;\r\n  margin: 0;\r\n}\r\n\r\n.logout-button {\r\n  font-family: 'Absender', sans-serif;\r\n  background-color: rgba(244, 67, 54, 0.2);\r\n  color: white;\r\n  border: 1px solid rgba(244, 67, 54, 0.5);\r\n  border-radius: 4px;\r\n  padding: 0.5rem 1rem;\r\n  font-size: 0.9rem;\r\n  cursor: pointer;\r\n  transition: background-color 0.2s;\r\n}\r\n\r\n.logout-button:hover {\r\n  background-color: rgba(244, 67, 54, 0.3);\r\n}\r\n\r\n.dashboard-actions {\r\n  display: flex;\r\n  gap: 1rem;\r\n  margin-bottom: 2rem;\r\n}\r\n\r\n.action-button {\r\n  flex: 1;\r\n  padding: 1rem;\r\n  background-color: rgba(255, 255, 255, 0.1);\r\n  color: white;\r\n  border: none;\r\n  border-radius: 4px;\r\n  font-size: 1rem;\r\n  font-weight: bold;\r\n  cursor: pointer;\r\n  transition: background-color 0.2s;\r\n}\r\n\r\n.action-button:hover {\r\n  background-color: rgba(255, 255, 255, 0.2);\r\n}\r\n\r\n/* Board Creation Section */\r\n.board-creation-section {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  gap: 1.5rem;\r\n  padding: 2rem;\r\n  background-color: rgba(255, 255, 255, 0.1);\r\n  border-radius: 8px;\r\n  width: 100%;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.board-creation-section h2 {\r\n  font-family: 'Absender', sans-serif;\r\n  color: white;\r\n  font-size: 1.5rem;\r\n  margin: 0;\r\n  text-align: center;\r\n}\r\n\r\n.start-playing-button {\r\n  font-family: 'Absender', sans-serif;\r\n  padding: 1rem 2rem;\r\n  font-size: 1.2rem;\r\n  font-weight: bold;\r\n  border: none;\r\n  border-radius: 8px;\r\n  cursor: pointer;\r\n  transition: all 0.2s ease;\r\n  text-transform: uppercase;\r\n  letter-spacing: 1px;\r\n}\r\n\r\n.start-playing-button.enabled {\r\n  background-color: #cc5500;\r\n  color: white;\r\n  box-shadow: 0 4px 15px rgba(204, 85, 0, 0.3);\r\n}\r\n\r\n.start-playing-button.enabled:hover {\r\n  background-color: #b84700;\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 6px 20px rgba(204, 85, 0, 0.4);\r\n}\r\n\r\n.start-playing-button.disabled {\r\n  background-color: rgba(255, 255, 255, 0.1);\r\n  color: rgba(255, 255, 255, 0.5);\r\n  cursor: not-allowed;\r\n  box-shadow: none;\r\n}\r\n\r\n.start-playing-button:disabled {\r\n  background-color: rgba(255, 255, 255, 0.1);\r\n  color: rgba(255, 255, 255, 0.5);\r\n  cursor: not-allowed;\r\n  box-shadow: none;\r\n}\r\n\r\n/* Agreement styles for modal */\r\n.agreement-section {\r\n  width: 100%;\r\n  display: flex;\r\n  justify-content: center;\r\n}\r\n\r\n.agreement-checkbox {\r\n  display: flex;\r\n  align-items: flex-start;\r\n  gap: 0.75rem;\r\n  color: white;\r\n  font-size: 1rem;\r\n  line-height: 1.5;\r\n  cursor: pointer;\r\n  max-width: 500px;\r\n  text-align: left;\r\n}\r\n\r\n.agreement-checkbox input[type=\"checkbox\"] {\r\n  width: 20px;\r\n  height: 20px;\r\n  margin: 0;\r\n  cursor: pointer;\r\n  accent-color: #4fc3f7;\r\n  flex-shrink: 0;\r\n  margin-top: 2px; /* Align with first line of text */\r\n}\r\n\r\n.agreement-checkbox a {\r\n  color: #4fc3f7;\r\n  text-decoration: none;\r\n  font-weight: bold;\r\n  transition: color 0.2s ease;\r\n}\r\n\r\n.agreement-checkbox a:hover {\r\n  color: #81d4fa;\r\n  text-decoration: underline;\r\n}\r\n\r\n.users-section {\r\n  background-color: rgba(255, 255, 255, 0.1);\r\n  border-radius: 8px;\r\n  padding: 1.5rem;\r\n  margin-top: 2rem;\r\n}\r\n\r\n.users-section h2 {\r\n  color: white;\r\n  margin-top: 0;\r\n  margin-bottom: 1rem;\r\n  font-size: 1.5rem;\r\n}\r\n\r\n.users-list {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 0.75rem;\r\n}\r\n\r\n.user-item {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 0.75rem;\r\n  background-color: rgba(0, 0, 0, 0.2);\r\n  border-radius: 4px;\r\n}\r\n\r\n.user-name {\r\n  color: white;\r\n  font-weight: bold;\r\n}\r\n\r\n.view-board-button {\r\n  background-color: rgba(204, 85, 0, 0.2);\r\n  color: white;\r\n  border: none;\r\n  border-radius: 4px;\r\n  padding: 0.5rem 1rem;\r\n  font-size: 0.9rem;\r\n  cursor: pointer;\r\n  transition: background-color 0.2s;\r\n}\r\n\r\n.view-board-button:hover {\r\n  background-color: rgba(204, 85, 0, 0.3);\r\n}\r\n\r\n.no-users-message {\r\n  color: rgba(255, 255, 255, 0.7);\r\n  text-align: center;\r\n  padding: 1rem;\r\n}\r\n\r\n/* Board Viewer Styles */\r\n.board-viewer-container {\r\n  width: 100%;\r\n  max-width: 800px;\r\n  padding: 1rem;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n}\r\n\r\n.board-viewer-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 2rem;\r\n}\r\n\r\n.board-viewer-header h1 {\r\n  font-family: 'Absender', sans-serif;\r\n  color: rgba(255, 255, 255, 0.8);\r\n  font-size: 2.2rem;\r\n  margin: 0;\r\n}\r\n\r\n/* Your Board text styling */\r\n.your-board-title {\r\n  text-decoration: underline;\r\n  opacity: 0.7; /* More transparent */\r\n}\r\n\r\n.back-button {\r\n  font-family: 'Absender', sans-serif;\r\n  background-color: rgba(204, 85, 0, 0.2);\r\n  color: white;\r\n  border: none;\r\n  border-radius: 4px;\r\n  padding: 0.75rem 1.5rem;\r\n  font-size: 1rem;\r\n  cursor: pointer;\r\n  transition: background-color 0.2s;\r\n  margin: 1.5rem auto;\r\n  display: block;\r\n  width: fit-content;\r\n}\r\n\r\n.back-button:hover {\r\n  background-color: rgba(204, 85, 0, 0.3);\r\n}\r\n\r\n/* Read-only styles */\r\n.bingo-square.read-only {\r\n  cursor: default;\r\n}\r\n\r\n.bingo-square.read-only:hover {\r\n  transform: none;\r\n  box-shadow: none;\r\n}\r\n\r\n/* App Header */\r\n.app-header {\r\n  margin-bottom: 1rem;\r\n  width: 100%;\r\n  display: flex;\r\n  justify-content: center;\r\n}\r\n\r\n.header-row {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 1rem;\r\n  padding: 0.5rem 1rem;\r\n  max-width: 800px;\r\n  width: 100%;\r\n}\r\n\r\n.logo-container {\r\n  flex-shrink: 0;\r\n}\r\n\r\n.title-logo {\r\n  height: 80px;\r\n  width: auto;\r\n}\r\n\r\n.thank-you-message {\r\n  flex: 1;\r\n}\r\n\r\n.thank-you-message p {\r\n  font-family: 'Vividly', sans-serif;\r\n  font-size: 1.1rem;\r\n  color: rgba(255, 255, 255, 0.8);\r\n  margin: 0;\r\n}\r\n\r\n/* Main Navigation */\r\n.main-navigation {\r\n  margin-bottom: 2rem;\r\n  width: 100%;\r\n}\r\n\r\n.nav-container {\r\n  display: flex;\r\n  justify-content: center;\r\n  flex-wrap: wrap;\r\n  padding: 0.75rem 1rem;\r\n  background-color: rgba(0, 0, 0, 0.15);\r\n  border-radius: 25px;\r\n  max-width: 800px;\r\n  margin: 0 auto;\r\n}\r\n\r\n.nav-item {\r\n  font-family: 'Absender', sans-serif;\r\n  color: white;\r\n  text-decoration: none;\r\n  padding: 0.75rem 1.5rem;\r\n  font-weight: 500;\r\n  transition: all 0.2s ease-in-out;\r\n  white-space: nowrap;\r\n  position: relative;\r\n}\r\n\r\n.nav-item:not(:last-child)::after {\r\n  content: '';\r\n  position: absolute;\r\n  right: 0;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  width: 1px;\r\n  height: 60%;\r\n  background-color: rgba(255, 255, 255, 0.3);\r\n}\r\n\r\n.nav-item:hover {\r\n  color: #cc5500;\r\n  transform: translateY(-1px);\r\n}\r\n\r\n.nav-item.active {\r\n  color: #cc5500;\r\n  font-weight: bold;\r\n}\r\n\r\n/* Bingo Board */\r\n.bingo-board {\r\n  display: grid;\r\n  grid-template-columns: repeat(5, 1fr);\r\n  gap: min(1.2vw, 0.5rem); /* Increased gap to prevent border overlap */\r\n  padding: min(1vw, 0.5rem); /* Dynamic padding that scales with viewport */\r\n  background-color: rgba(243, 244, 246, 0.1); /* More transparent background */\r\n  border-radius: 0.5rem;\r\n  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);\r\n  width: min(95vw, 95%); /* Use viewport width but cap at 95% */\r\n  aspect-ratio: 1/1; /* Maintain square aspect ratio */\r\n  max-height: 95vh; /* Ensure it doesn't exceed viewport height */\r\n  max-width: 95vh; /* Keep it square by matching max-height */\r\n}\r\n\r\n/* Bingo Square */\r\n.bingo-square {\r\n  width: 95%; /* Slightly reduced from 100% to create space between squares */\r\n  height: 95%; /* Slightly reduced from 100% to create space between squares */\r\n  border: min(0.5vw, 3px) solid; /* Dynamic border that scales with viewport */\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  cursor: pointer;\r\n  transition: all 0.2s ease-in-out;\r\n  border-radius: 0; /* Removed curved corners */\r\n  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);\r\n  overflow: hidden;\r\n  position: relative;\r\n  padding: 0;\r\n  aspect-ratio: 1/1; /* Ensure square aspect ratio */\r\n  margin: auto; /* Center the square in its grid cell */\r\n}\r\n\r\n/* Character info styles removed as they're no longer needed */\r\n\r\n.thumbnail-container {\r\n  width: 100%;\r\n  height: 100%;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  overflow: hidden; /* Ensure images don't overflow */\r\n  cursor: pointer;\r\n  position: relative; /* Added for absolute positioning of stars */\r\n}\r\n\r\n.character-thumbnail {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: cover; /* Cover the container while maintaining aspect ratio */\r\n  object-position: center; /* Center the image */\r\n  max-width: 100%; /* Ensure image doesn't exceed container width */\r\n  max-height: 100%; /* Ensure image doesn't exceed container height */\r\n  transition: transform 0.2s ease;\r\n}\r\n\r\n.thumbnail-container:hover .character-thumbnail {\r\n  transform: scale(1.05);\r\n}\r\n\r\n.thumbnail-container:active .character-thumbnail {\r\n  transform: scale(0.95);\r\n}\r\n\r\n/* Star rating system for rarity */\r\n.rarity-stars {\r\n  position: absolute;\r\n  bottom: 5px;\r\n  left: 0;\r\n  width: 100%;\r\n  display: flex;\r\n  justify-content: center;\r\n  gap: 2px;\r\n  z-index: 4; /* Below the claimed overlay but above the thumbnail */\r\n  pointer-events: none; /* Ensure clicks pass through to the thumbnail */\r\n}\r\n\r\n.star {\r\n  font-size: 18px; /* Increased from 14px */\r\n  filter: drop-shadow(0 0 2px rgba(0, 0, 0, 0.7)); /* Add shadow for better visibility */\r\n}\r\n\r\n/* Removed bingo-square:hover since borders are no longer clickable */\r\n\r\n.bingo-square.marked {\r\n  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);\r\n  position: relative;\r\n}\r\n\r\n/* Claimed overlay with translucent black background and white check mark */\r\n.bingo-square.marked::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  background-color: rgba(0, 0, 0, 0.45); /* 45% opacity black overlay */\r\n  z-index: 5; /* Lower z-index to ensure it doesn't block clicks */\r\n  pointer-events: none; /* Ensure clicks pass through to the thumbnail */\r\n}\r\n\r\n.bingo-square.marked::after {\r\n  content: 'CLAIMED';\r\n  position: absolute;\r\n  top: 50%;\r\n  left: 50%;\r\n  transform: translate(-50%, -50%);\r\n  color: rgba(255, 255, 255, 0.7); /* Semi-transparent white checkmark */\r\n  font-size: min(60%, 1.8rem); /* Increased font size */\r\n  font-weight: bold;\r\n  z-index: 6; /* Above the overlay but still low enough to not block clicks */\r\n  pointer-events: none; /* Ensure clicks pass through to the thumbnail */\r\n  text-align: center; /* Ensure text is centered */\r\n  width: 100%; /* Take full width of container */\r\n}\r\n\r\n/* Special styling for the FREE square */\r\n.bingo-square.free.marked::after {\r\n  content: 'FREE';\r\n  font-size: min(70%, 3.5rem); /* Increased font size */\r\n}\r\n\r\n/* Points and Controls Container */\r\n.points-controls-container {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: flex-end;\r\n  gap: 1.5rem; /* Increased gap for more spacing between meter and score */\r\n  margin-top: min(4vh, 1.5rem);\r\n  width: min(95%, 800px); /* Use 95% of container width but cap at 800px */\r\n  position: relative;\r\n}\r\n\r\n/* Controls wrapper for score display and refresh button */\r\n.controls-wrapper {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 1rem;\r\n}\r\n\r\n/* Points Display */\r\n.points-display {\r\n  width: 100px;\r\n  height: 100px;\r\n  padding: min(2vw, 0.8rem);\r\n  background-color: rgba(255, 255, 255, 0.1);\r\n  border-radius: 0.5rem;\r\n  color: white;\r\n  box-sizing: border-box; /* Include padding in width calculation */\r\n  text-align: center;\r\n  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: center;\r\n  align-items: center;\r\n}\r\n\r\n.score-label {\r\n  font-size: 0.9rem;\r\n  text-transform: uppercase;\r\n  letter-spacing: 1px;\r\n  opacity: 0.8;\r\n  margin-bottom: 0.3rem;\r\n}\r\n\r\n.points-display .score-value {\r\n  font-size: 2.2rem;\r\n  font-weight: bold;\r\n  color: white;\r\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);\r\n  position: relative; /* Ensure proper positioning */\r\n}\r\n\r\n/* Points Meter */\r\n.points-meter-container {\r\n  position: absolute;\r\n  left: 0;\r\n  height: 100px;\r\n  width: calc(100% - 200px); /* Adjusted space for score and refresh button with wrapper */\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.points-meter {\r\n  width: 100%;\r\n  height: 100px; /* Match the height of the score container */\r\n  background-color: rgba(255, 255, 255, 0.1);\r\n  border-radius: 15px;\r\n  position: relative;\r\n  margin: auto; /* Center vertically */\r\n  overflow: hidden; /* Ensure fill is contained */\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n}\r\n\r\n.points-meter-fill {\r\n  height: 100%;\r\n  background: white;\r\n  border-radius: 15px;\r\n  transition: width 0.5s ease-out;\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  z-index: 1;\r\n}\r\n\r\n.progress-text {\r\n  font-size: 2.8rem; /* Bigger text */\r\n  font-weight: 900; /* Extra bold for blockier appearance */\r\n  text-transform: uppercase;\r\n  letter-spacing: 1px; /* Tighter letter spacing to fit better */\r\n  position: absolute;\r\n  z-index: 2;\r\n  mix-blend-mode: difference; /* Creates the negative color effect */\r\n  color: rgba(255, 255, 255, 0.7); /* More translucent white for unfilled area */\r\n  text-align: center;\r\n  pointer-events: none; /* Ensures clicks pass through to the meter */\r\n  width: 100%;\r\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);\r\n  font-family: Arial, Helvetica, sans-serif; /* More blocky font */\r\n  line-height: 1; /* Tighter line height */\r\n}\r\n\r\n/* Milestone markers (without tooltips) */\r\n.milestone-marker {\r\n  position: absolute;\r\n  bottom: 0;\r\n  transform: translateX(-50%);\r\n  width: 2px;\r\n  height: 100px; /* Match the height of the progress bar */\r\n  z-index: 10;\r\n}\r\n\r\n.milestone-marker::before {\r\n  content: '';\r\n  position: absolute;\r\n  bottom: 0;\r\n  left: 0;\r\n  width: 2px;\r\n  height: 15px; /* Small notch at the bottom */\r\n  background-color: white; /* White notch for visibility */\r\n  z-index: 10;\r\n  pointer-events: none;\r\n}\r\n\r\n.milestone-value {\r\n  position: absolute;\r\n  bottom: -25px;\r\n  left: 50%;\r\n  transform: translateX(-50%);\r\n  color: white;\r\n  font-size: 0.9rem;\r\n  white-space: nowrap;\r\n  z-index: 12;\r\n  pointer-events: none;\r\n  width: max-content;\r\n  text-align: center;\r\n}\r\n\r\n/* Next Reward Section */\r\n.next-reward-section {\r\n  margin-top: 1rem;\r\n  text-align: center;\r\n  color: white;\r\n}\r\n\r\n.next-reward-label {\r\n  font-family: 'Absender', sans-serif;\r\n  font-size: 1rem;\r\n  font-weight: bold;\r\n  margin-bottom: 0.5rem;\r\n  color: rgba(255, 255, 255, 0.8);\r\n}\r\n\r\n.next-reward-text {\r\n  font-family: 'Vividly', sans-serif;\r\n  font-size: 1.1rem;\r\n  font-weight: bold;\r\n  color: #4fc3f7;\r\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n/* Refresh Button */\r\n.refresh-button {\r\n  width: 50px;\r\n  height: 50px;\r\n  border-radius: 50%;\r\n  background-color: rgba(255, 255, 255, 0.1);\r\n  border: none;\r\n  color: white;\r\n  font-size: 24px;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  cursor: pointer;\r\n  transition: all 0.2s ease;\r\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.refresh-button:hover {\r\n  background-color: rgba(255, 255, 255, 0.2);\r\n  transform: rotate(30deg);\r\n}\r\n\r\n.refresh-button:active {\r\n  transform: rotate(60deg) scale(0.95);\r\n}\r\n\r\n/* Confirmation Modal */\r\n.confirmation-overlay {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  background-color: rgba(0, 0, 0, 0.7);\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  z-index: 1000;\r\n  backdrop-filter: blur(5px);\r\n}\r\n\r\n.confirmation-dialog {\r\n  background-color: #2c3e50;\r\n  border-radius: 8px;\r\n  padding: 1.5rem;\r\n  width: min(90%, 400px);\r\n  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);\r\n  text-align: center;\r\n  color: white;\r\n  animation: modal-appear 0.3s ease-out;\r\n}\r\n\r\n@keyframes modal-appear {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateY(-20px);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateY(0);\r\n  }\r\n}\r\n\r\n.confirmation-dialog h3 {\r\n  margin-top: 0;\r\n  color: #f44336;\r\n  font-size: 1.5rem;\r\n}\r\n\r\n.confirmation-dialog p {\r\n  margin-bottom: 1.5rem;\r\n  line-height: 1.5;\r\n}\r\n\r\n.confirmation-buttons {\r\n  display: flex;\r\n  justify-content: center;\r\n  gap: 1rem;\r\n}\r\n\r\n.confirmation-buttons button {\r\n  padding: 0.75rem 1.5rem;\r\n  border: none;\r\n  border-radius: 4px;\r\n  font-weight: bold;\r\n  cursor: pointer;\r\n  transition: background-color 0.2s;\r\n}\r\n\r\n.cancel-button {\r\n  background-color: #78909c;\r\n  color: white;\r\n}\r\n\r\n.cancel-button:hover {\r\n  background-color: #607d8b;\r\n}\r\n\r\n.confirm-button {\r\n  background-color: #f44336;\r\n  color: white;\r\n}\r\n\r\n.confirm-button:hover {\r\n  background-color: #d32f2f;\r\n}\r\n\r\n.confirm-button.disabled {\r\n  background-color: #b0bec5;\r\n  cursor: not-allowed;\r\n}\r\n\r\n.confirm-button.disabled:hover {\r\n  background-color: #b0bec5;\r\n}\r\n\r\n/* Agreement Modal */\r\n.agreement-modal {\r\n  background-color: #2c3e50;\r\n  border-radius: 8px;\r\n  padding: 2rem;\r\n  width: min(90%, 500px);\r\n  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);\r\n  text-align: center;\r\n  color: white;\r\n  animation: modal-appear 0.3s ease-out;\r\n}\r\n\r\n.agreement-modal h3 {\r\n  margin-top: 0;\r\n  color: #4fc3f7;\r\n  font-size: 1.5rem;\r\n  margin-bottom: 1.5rem;\r\n}\r\n\r\n.agreement-text {\r\n  margin-bottom: 2rem;\r\n  text-align: left;\r\n}\r\n\r\n.agreement-text p {\r\n  margin: 0;\r\n  line-height: 1.6;\r\n  color: rgba(255, 255, 255, 0.9);\r\n  font-size: 1rem;\r\n}\r\n\r\n.agreement-text a {\r\n  color: #4fc3f7;\r\n  text-decoration: none;\r\n  font-weight: bold;\r\n  transition: color 0.2s ease;\r\n}\r\n\r\n.agreement-text a:hover {\r\n  color: #81d4fa;\r\n  text-decoration: underline;\r\n}\r\n\r\n.agreement-checkbox-section {\r\n  margin-bottom: 2rem;\r\n  display: flex;\r\n  justify-content: center;\r\n}\r\n\r\n.agreement-modal .agreement-checkbox {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.75rem;\r\n  color: white;\r\n  font-size: 1.1rem;\r\n  font-weight: bold;\r\n  cursor: pointer;\r\n  text-align: left;\r\n}\r\n\r\n.agreement-modal .agreement-checkbox input[type=\"checkbox\"] {\r\n  width: 20px;\r\n  height: 20px;\r\n  margin: 0;\r\n  cursor: pointer;\r\n  accent-color: #4fc3f7;\r\n  flex-shrink: 0;\r\n}\r\n\r\n/* Leaderboard Section */\r\n.leaderboard-section {\r\n  margin-top: min(4vh, 1.5rem);\r\n  padding: min(5vw, 1.5rem);\r\n  background-color: rgba(255, 255, 255, 0.1);\r\n  border-radius: 0.5rem;\r\n  width: min(95%, 800px); /* Use 95% of container width but cap at 800px */\r\n  color: white;\r\n  box-sizing: border-box; /* Include padding in width calculation */\r\n}\r\n\r\n.leaderboard-section h2 {\r\n  font-size: 1.5rem;\r\n  margin-bottom: 1rem;\r\n  color: white;\r\n  border-bottom: 1px solid rgba(255, 255, 255, 0.3);\r\n  padding-bottom: 0.5rem;\r\n}\r\n\r\n.leaderboard-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 1rem;\r\n}\r\n\r\n.leaderboard-entry {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 1rem;\r\n  padding: 0.5rem;\r\n  background-color: rgba(0, 0, 0, 0.2);\r\n  border-radius: 0.5rem;\r\n  transition: transform 0.2s ease;\r\n}\r\n\r\n.leaderboard-entry:hover {\r\n  transform: translateX(5px);\r\n  background-color: rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n.user-rank {\r\n  width: 2rem;\r\n  height: 2rem;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background-color: rgba(255, 255, 255, 0.1);\r\n  border-radius: 50%;\r\n  font-weight: bold;\r\n  font-size: 1.2rem;\r\n}\r\n\r\n.user-name {\r\n  width: 8rem; /* Increased width to accommodate 16 characters */\r\n  font-weight: bold;\r\n  font-size: 1.1rem;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n}\r\n\r\n.score-bar-container {\r\n  flex: 1;\r\n  height: 2rem;\r\n  background-color: rgba(0, 0, 0, 0.3);\r\n  border-radius: 1rem;\r\n  position: relative;\r\n  overflow: hidden;\r\n  display: flex;\r\n  align-items: center;\r\n  cursor: pointer; /* Make it clickable */\r\n  transition: background-color 0.2s ease;\r\n}\r\n\r\n.score-bar-container:hover {\r\n  background-color: rgba(0, 0, 0, 0.4); /* Darker on hover */\r\n}\r\n\r\n.score-bar {\r\n  height: 100%;\r\n  background: white;\r\n  border-radius: 1rem;\r\n  transition: width 0.5s ease-out;\r\n}\r\n\r\n.score-number {\r\n  width: 3rem;\r\n  text-align: right;\r\n  font-weight: bold;\r\n  font-size: 1.1rem;\r\n  color: white;\r\n  flex-shrink: 0; /* Prevent shrinking */\r\n}\r\n\r\n/* Rules Section */\r\n.rules-section {\r\n  margin-top: min(4vh, 1.5rem);\r\n  padding: min(5vw, 1.5rem);\r\n  background-color: rgba(255, 255, 255, 0.1);\r\n  border-radius: 0.5rem;\r\n  width: min(95%, 800px); /* Use 95% of container width but cap at 800px */\r\n  color: white;\r\n  box-sizing: border-box; /* Include padding in width calculation */\r\n}\r\n\r\n.rules-section h2 {\r\n  font-size: 1.5rem;\r\n  margin-bottom: 1rem;\r\n  color: white;\r\n  border-bottom: 1px solid rgba(255, 255, 255, 0.3);\r\n  padding-bottom: 0.5rem;\r\n}\r\n\r\n.rules-section p {\r\n  font-size: 1rem;\r\n  line-height: 1.6;\r\n}\r\n\r\n/* Dashboard Info Sections */\r\n.dashboard-info-sections {\r\n  width: 100%;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  margin-top: 2rem;\r\n  margin-bottom: 2rem;\r\n  padding-top: 1rem;\r\n  border-top: 1px solid rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n.rules-container {\r\n  width: 100%;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n}\r\n\r\n/* Header Links */\r\n.header-links {\r\n  font-size: 0.9rem;\r\n  margin-left: 0.5rem;\r\n}\r\n\r\n.header-links a {\r\n  color: #61dafb;\r\n  text-decoration: none;\r\n  margin: 0 0.3rem;\r\n  transition: color 0.2s ease;\r\n}\r\n\r\n.header-links a:hover {\r\n  color: white;\r\n  text-decoration: underline;\r\n}\r\n\r\n/* Standalone Rules Page */\r\n.standalone-rules-page {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  min-height: 60vh;\r\n  width: 100%;\r\n  padding: 2rem 0;\r\n}\r\n\r\n/* How to Play Page - WikiHow Style */\r\n.how-to-play-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  min-height: 60vh;\r\n  width: 100%;\r\n  padding: 2rem 0;\r\n  color: white;\r\n}\r\n\r\n.how-to-play-header {\r\n  text-align: center;\r\n  margin-bottom: 3rem;\r\n  max-width: 900px;\r\n}\r\n\r\n.how-to-play-header h1 {\r\n  font-family: 'Absender', sans-serif;\r\n  font-size: 3rem;\r\n  margin-bottom: 0.5rem;\r\n  color: #4fc3f7;\r\n  font-weight: bold;\r\n  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);\r\n}\r\n\r\n.wikihow-subtitle {\r\n  font-family: 'Vividly', sans-serif;\r\n  font-size: 1.4rem;\r\n  color: rgba(255, 255, 255, 0.9);\r\n  margin-bottom: 1.5rem;\r\n  font-style: italic;\r\n}\r\n\r\n.wikihow-meta {\r\n  display: flex;\r\n  justify-content: center;\r\n  gap: 2rem;\r\n  flex-wrap: wrap;\r\n  margin-top: 1rem;\r\n}\r\n\r\n.wikihow-meta span {\r\n  font-family: 'Absender', sans-serif;\r\n  background-color: rgba(79, 195, 247, 0.2);\r\n  padding: 0.5rem 1rem;\r\n  border-radius: 20px;\r\n  font-size: 0.9rem;\r\n  border: 1px solid rgba(79, 195, 247, 0.3);\r\n}\r\n\r\n.how-to-play-content {\r\n  width: min(95%, 1000px);\r\n  box-sizing: border-box;\r\n}\r\n\r\n/* WikiHow Intro */\r\n.wikihow-intro {\r\n  background-color: rgba(255, 255, 255, 0.1);\r\n  padding: 2rem;\r\n  border-radius: 1rem;\r\n  margin-bottom: 3rem;\r\n  border-left: 5px solid #4fc3f7;\r\n}\r\n\r\n.wikihow-intro p {\r\n  font-family: 'Vividly', sans-serif;\r\n  font-size: 1.1rem;\r\n  line-height: 1.7;\r\n  margin: 0;\r\n  color: rgba(255, 255, 255, 0.9);\r\n}\r\n\r\n/* WikiHow Steps */\r\n.wikihow-steps {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 3rem;\r\n}\r\n\r\n.wikihow-step {\r\n  background-color: rgba(255, 255, 255, 0.08);\r\n  border-radius: 1.5rem;\r\n  padding: 2.5rem;\r\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);\r\n  border: 1px solid rgba(255, 255, 255, 0.1);\r\n  transition: transform 0.3s ease, box-shadow 0.3s ease;\r\n}\r\n\r\n.wikihow-step:hover {\r\n  transform: translateY(-5px);\r\n  box-shadow: 0 12px 40px rgba(79, 195, 247, 0.2);\r\n}\r\n\r\n.step-header {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 1.5rem;\r\n  gap: 1rem;\r\n}\r\n\r\n.step-number {\r\n  font-family: 'Absender', sans-serif;\r\n  background: linear-gradient(135deg, #4fc3f7, #29b6f6);\r\n  color: white;\r\n  width: 3rem;\r\n  height: 3rem;\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 1.5rem;\r\n  font-weight: bold;\r\n  box-shadow: 0 4px 15px rgba(79, 195, 247, 0.4);\r\n  flex-shrink: 0;\r\n}\r\n\r\n.step-title {\r\n  font-family: 'Absender', sans-serif;\r\n  font-size: 1.8rem;\r\n  color: #4fc3f7;\r\n  margin: 0;\r\n  font-weight: bold;\r\n}\r\n\r\n.step-body {\r\n  display: grid;\r\n  gap: 2rem;\r\n  align-items: center;\r\n}\r\n\r\n/* Alternating layout */\r\n.wikihow-step.left .step-body {\r\n  grid-template-columns: 1fr 300px;\r\n}\r\n\r\n.wikihow-step.right .step-body {\r\n  grid-template-columns: 300px 1fr;\r\n}\r\n\r\n.wikihow-step.right .step-text {\r\n  order: 2;\r\n}\r\n\r\n.wikihow-step.right .step-image {\r\n  order: 1;\r\n}\r\n\r\n.step-text p {\r\n  font-family: 'Vividly', sans-serif;\r\n  font-size: 1.1rem;\r\n  line-height: 1.7;\r\n  color: rgba(255, 255, 255, 0.9);\r\n  margin: 0;\r\n}\r\n\r\n.step-image {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n}\r\n\r\n.wikihow-image {\r\n  max-width: 100%;\r\n  height: auto;\r\n  border-radius: 1rem;\r\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);\r\n  border: 3px solid rgba(79, 195, 247, 0.3);\r\n  transition: transform 0.3s ease, box-shadow 0.3s ease;\r\n}\r\n\r\n.wikihow-image:hover {\r\n  transform: scale(1.05);\r\n  box-shadow: 0 12px 35px rgba(79, 195, 247, 0.4);\r\n}\r\n\r\n/* WikiHow Conclusion */\r\n.wikihow-conclusion {\r\n  background: linear-gradient(135deg, rgba(76, 175, 80, 0.2), rgba(139, 195, 74, 0.2));\r\n  padding: 2.5rem;\r\n  border-radius: 1.5rem;\r\n  margin-top: 3rem;\r\n  text-align: center;\r\n  border: 2px solid rgba(76, 175, 80, 0.3);\r\n}\r\n\r\n.wikihow-conclusion h3 {\r\n  font-family: 'Absender', sans-serif;\r\n  font-size: 2rem;\r\n  color: #81c784;\r\n  margin-bottom: 1rem;\r\n  font-weight: bold;\r\n}\r\n\r\n.wikihow-conclusion p {\r\n  font-family: 'Vividly', sans-serif;\r\n  font-size: 1.2rem;\r\n  line-height: 1.6;\r\n  color: rgba(255, 255, 255, 0.9);\r\n  margin: 0;\r\n}\r\n\r\n/* Responsive Design for WikiHow */\r\n@media (max-width: 768px) {\r\n  .how-to-play-header h1 {\r\n    font-size: 2.2rem;\r\n  }\r\n\r\n  .wikihow-subtitle {\r\n    font-size: 1.1rem;\r\n  }\r\n\r\n  .wikihow-meta {\r\n    gap: 1rem;\r\n  }\r\n\r\n  .wikihow-meta span {\r\n    font-family: 'Absender', sans-serif;\r\n    font-size: 0.8rem;\r\n    padding: 0.4rem 0.8rem;\r\n  }\r\n\r\n  .wikihow-step {\r\n    padding: 1.5rem;\r\n  }\r\n\r\n  .step-number {\r\n    width: 2.5rem;\r\n    height: 2.5rem;\r\n    font-size: 1.2rem;\r\n  }\r\n\r\n  .step-title {\r\n    font-size: 1.4rem;\r\n  }\r\n\r\n  /* Stack layout on mobile */\r\n  .wikihow-step.left .step-body,\r\n  .wikihow-step.right .step-body {\r\n    grid-template-columns: 1fr;\r\n    gap: 1.5rem;\r\n  }\r\n\r\n  .wikihow-step.right .step-text,\r\n  .wikihow-step.right .step-image {\r\n    order: unset;\r\n  }\r\n\r\n  .step-text p {\r\n    font-size: 1rem;\r\n  }\r\n\r\n  .wikihow-intro {\r\n    padding: 1.5rem;\r\n  }\r\n\r\n  .wikihow-intro p {\r\n    font-size: 1rem;\r\n  }\r\n\r\n  .wikihow-conclusion {\r\n    padding: 1.5rem;\r\n  }\r\n\r\n  .wikihow-conclusion h3 {\r\n    font-size: 1.6rem;\r\n  }\r\n\r\n  .wikihow-conclusion p {\r\n    font-size: 1rem;\r\n  }\r\n}\r\n\r\n/* Cards Component */\r\n.cards-container {\r\n  width: 100%;\r\n  max-width: 1200px;\r\n  padding: 1rem;\r\n  color: white;\r\n}\r\n\r\n.cards-container h1 {\r\n  font-family: 'Absender', sans-serif;\r\n  text-align: center;\r\n  color: #4fc3f7;\r\n  font-size: 2.5rem;\r\n  margin-bottom: 2rem;\r\n  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);\r\n}\r\n\r\n.cards-content {\r\n  width: 100%;\r\n}\r\n\r\n.rarity-section {\r\n  margin-bottom: 3rem;\r\n}\r\n\r\n.rarity-title {\r\n  font-family: 'Absender', sans-serif;\r\n  font-size: 1.8rem;\r\n  font-weight: bold;\r\n  margin-bottom: 1rem;\r\n  text-align: center;\r\n  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);\r\n}\r\n\r\n.cards-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));\r\n  gap: 1.5rem;\r\n  padding: 0 1rem;\r\n}\r\n\r\n.card-item {\r\n  background-color: rgba(255, 255, 255, 0.1);\r\n  border-radius: 12px;\r\n  padding: 1rem;\r\n  text-align: center;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  border: 2px solid transparent;\r\n}\r\n\r\n.card-item:hover {\r\n  transform: translateY(-5px);\r\n  background-color: rgba(255, 255, 255, 0.15);\r\n  border-color: rgba(79, 195, 247, 0.5);\r\n  box-shadow: 0 8px 25px rgba(79, 195, 247, 0.3);\r\n}\r\n\r\n.card-image-container {\r\n  position: relative;\r\n  margin-bottom: 0.75rem;\r\n}\r\n\r\n.card-thumbnail {\r\n  width: 100%;\r\n  height: 150px;\r\n  object-fit: cover;\r\n  border-radius: 8px;\r\n}\r\n\r\n.card-frame {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: cover;\r\n  pointer-events: none;\r\n}\r\n\r\n.card-info {\r\n  text-align: center;\r\n}\r\n\r\n.card-name {\r\n  font-family: 'Absender', sans-serif;\r\n  font-size: 1.1rem;\r\n  font-weight: bold;\r\n  color: white;\r\n  margin: 0 0 0.25rem 0;\r\n}\r\n\r\n.card-source {\r\n  font-family: 'Vividly', sans-serif;\r\n  font-size: 0.9rem;\r\n  color: rgba(255, 255, 255, 0.7);\r\n  margin: 0;\r\n}\r\n\r\n.loading-message, .error-message {\r\n  font-family: 'Vividly', sans-serif;\r\n  text-align: center;\r\n  font-size: 1.2rem;\r\n  color: rgba(255, 255, 255, 0.8);\r\n  margin: 2rem 0;\r\n}\r\n\r\n.error-message {\r\n  color: #f44336;\r\n}\r\n\r\n/* App Footer */\r\n.app-footer {\r\n  margin-top: 2rem;\r\n  text-align: center;\r\n  color: rgba(255, 255, 255, 0.8);\r\n  font-size: 0.875rem;\r\n}\r\n\r\n/* Portrait Overlay */\r\n.portrait-overlay {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  background-color: rgba(0, 0, 0, 0);\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  z-index: 1000;\r\n  backdrop-filter: blur(0px);\r\n  transition: background-color 0.3s ease, backdrop-filter 0.3s ease;\r\n}\r\n\r\n.portrait-overlay.visible {\r\n  background-color: rgba(0, 0, 0, 0.85);\r\n  backdrop-filter: blur(5px);\r\n}\r\n\r\n.portrait-container {\r\n  position: relative;\r\n  max-width: 90%;\r\n  max-height: 90vh;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  transform: scale(0.2);\r\n  opacity: 0;\r\n  transition: transform 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275), opacity 0.3s ease;\r\n}\r\n\r\n.portrait-container.visible {\r\n  transform: scale(1);\r\n  opacity: 1;\r\n}\r\n\r\n.portrait-image-container {\r\n  position: relative;\r\n  display: inline-block;\r\n}\r\n\r\n.character-portrait {\r\n  max-width: 100%;\r\n  max-height: 80vh;\r\n  object-fit: contain;\r\n  border-radius: 4px;\r\n  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);\r\n  display: block;\r\n}\r\n\r\n.user-uploaded-portrait {\r\n  /* Scale to match the height of normal portraits */\r\n  height: 80vh;\r\n  max-width: 100%;\r\n  object-fit: contain;\r\n  border-radius: 4px;\r\n  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);\r\n  display: block;\r\n}\r\n\r\n.portrait-frame-overlay {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: contain;\r\n  pointer-events: none; /* Allow clicks to pass through to the portrait */\r\n  z-index: 1; /* Ensure it appears above the portrait */\r\n}\r\n\r\n.close-button {\r\n  position: absolute;\r\n  top: -40px;\r\n  right: -40px;\r\n  width: 36px;\r\n  height: 36px;\r\n  background-color: rgba(255, 255, 255, 0.2);\r\n  border: none;\r\n  border-radius: 50%;\r\n  color: white;\r\n  font-size: 24px;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  cursor: pointer;\r\n  transition: background-color 0.2s;\r\n  z-index: 1001;\r\n}\r\n\r\n.close-button:hover {\r\n  background-color: rgba(255, 255, 255, 0.4);\r\n}\r\n\r\n.portrait-buttons {\r\n  display: flex;\r\n  gap: 10px;\r\n  margin-top: 20px;\r\n  width: 100%; /* Match the portrait width */\r\n}\r\n\r\n.claim-button {\r\n  font-family: 'Absender', sans-serif;\r\n  padding: 10px 20px;\r\n  background-color: transparent;\r\n  color: white;\r\n  border: 2px solid white;\r\n  border-radius: 4px;\r\n  font-size: 18px;\r\n  font-weight: bold;\r\n  cursor: pointer;\r\n  transition: all 0.2s;\r\n  box-shadow: none;\r\n  position: relative;\r\n  flex: 3; /* Take 3/4 of the available space */\r\n}\r\n\r\n.claim-button:hover {\r\n  background-color: rgba(255, 255, 255, 0.1);\r\n  border-color: rgba(255, 255, 255, 0.8);\r\n}\r\n\r\n/* Unclaim button styling */\r\n.claim-button.unclaim {\r\n  background-color: transparent;\r\n  color: #F44336;\r\n  border-color: #F44336;\r\n}\r\n\r\n.claim-button.unclaim:hover {\r\n  background-color: rgba(244, 67, 54, 0.1);\r\n  border-color: rgba(244, 67, 54, 0.8);\r\n}\r\n\r\n.details-button {\r\n  font-family: 'Absender', sans-serif;\r\n  padding: 10px 15px;\r\n  background-color: #2196F3; /* Default color, will be overridden by inline style */\r\n  color: white;\r\n  border: none;\r\n  border-radius: 4px;\r\n  font-size: 18px;\r\n  font-weight: bold;\r\n  cursor: pointer;\r\n  transition: all 0.2s;\r\n  position: relative;\r\n  flex: 1; /* Take 1/4 of the available space */\r\n  /* Glow effect will be applied via inline style */\r\n}\r\n\r\n.details-button:hover {\r\n  transform: translateY(-1px);\r\n  filter: brightness(1.1);\r\n}\r\n\r\n.character-details-overlay {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  background-color: rgba(0, 0, 0, 0.85);\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  z-index: 10;\r\n  border-radius: 4px;\r\n  overflow: auto;\r\n  cursor: pointer; /* Indicate it's clickable */\r\n}\r\n\r\n.character-details-content {\r\n  color: white;\r\n  padding: 30px;\r\n  width: 100%;\r\n  height: 100%;\r\n  overflow-y: auto;\r\n  background-color: rgba(0, 0, 0, 0.9); /* Darker background for better readability */\r\n  border-radius: 4px;\r\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);\r\n  cursor: default; /* Reset cursor for content area */\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: center;\r\n}\r\n\r\n.character-details-content p {\r\n  font-family: 'Vividly', sans-serif;\r\n  margin-bottom: 8px;\r\n  line-height: 1.4;\r\n  font-size: 1.3em; /* Increased from default */\r\n}\r\n\r\n.character-details-content p strong {\r\n  font-family: 'Absender', sans-serif;\r\n  color: #81d4fa; /* Light blue for headers - will be overridden by inline styles */\r\n  font-size: 1.4em; /* Increased from 1.1em */\r\n  display: block;\r\n  margin-bottom: 2px;\r\n}\r\n\r\n/* Upload Overlay Styles */\r\n.upload-overlay-content {\r\n  text-align: center;\r\n}\r\n\r\n.upload-title {\r\n  color: #fff;\r\n  margin: 0 0 15px 0;\r\n  font-size: 1.4em;\r\n  font-family: 'Absender', sans-serif;\r\n}\r\n\r\n.upload-description {\r\n  color: rgba(255, 255, 255, 0.8);\r\n  margin: 0 0 25px 0;\r\n  font-size: 1.1em;\r\n  font-family: 'vividly', sans-serif;\r\n  line-height: 1.4;\r\n}\r\n\r\n.upload-buttons {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 15px;\r\n  align-items: center;\r\n}\r\n\r\n.upload-select-button,\r\n.upload-confirm-button,\r\n.upload-cancel-button {\r\n  min-width: 180px;\r\n  padding: 12px 24px;\r\n  font-size: 1em;\r\n  font-family: 'Absender', sans-serif;\r\n  border: none;\r\n  border-radius: 8px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  color: white;\r\n}\r\n\r\n.upload-select-button:hover:not(:disabled),\r\n.upload-confirm-button:hover:not(:disabled),\r\n.upload-cancel-button:hover:not(:disabled) {\r\n  transform: translateY(-2px);\r\n  filter: brightness(1.1);\r\n}\r\n\r\n.upload-select-button:disabled,\r\n.upload-confirm-button:disabled,\r\n.upload-cancel-button:disabled {\r\n  opacity: 0.6;\r\n  cursor: not-allowed;\r\n  transform: none;\r\n}\r\n\r\n.selected-file-info {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 15px;\r\n  align-items: center;\r\n  width: 100%;\r\n}\r\n\r\n.selected-file-info p {\r\n  font-family: 'Vividly', sans-serif;\r\n  margin-bottom: 2px;\r\n  line-height: 1.4;\r\n  font-size: 1.1em;\r\n}\r\n\r\n.selected-file-info p strong {\r\n  font-family: 'Absender', sans-serif;\r\n  font-size: 1.2em;\r\n  display: block;\r\n  margin-bottom: 2px;\r\n}\r\n\r\n.upload-actions {\r\n  display: flex;\r\n  gap: 15px;\r\n  flex-wrap: wrap;\r\n  justify-content: center;\r\n}\r\n\r\n/* Mobile adjustments for portrait overlay */\r\n@media (max-width: 768px) {\r\n  .portrait-container {\r\n    max-width: 95%;\r\n    transform: scale(0.1); /* Smaller initial scale on mobile for more dramatic effect */\r\n  }\r\n\r\n  .portrait-container.visible {\r\n    transform: scale(1);\r\n  }\r\n\r\n  .close-button {\r\n    top: -30px;\r\n    right: -10px;\r\n    width: 30px;\r\n    height: 30px;\r\n    font-size: 20px;\r\n  }\r\n\r\n  .portrait-buttons {\r\n    flex-direction: row; /* Keep horizontal layout on mobile */\r\n    gap: 8px;\r\n    width: 100%;\r\n  }\r\n\r\n  .claim-button, .details-button {\r\n    padding: 8px 16px;\r\n    font-size: 16px;\r\n  }\r\n\r\n  .claim-button {\r\n    border-width: 2px; /* Maintain border thickness on mobile */\r\n    flex: 3; /* Maintain 3/4 ratio on mobile */\r\n  }\r\n\r\n  .details-button {\r\n    flex: 1; /* Maintain 1/4 ratio on mobile */\r\n    padding: 8px 10px; /* Smaller padding for smaller button */\r\n  }\r\n\r\n  .character-details-content {\r\n    padding: 20px;\r\n    font-size: 16px; /* Increased from 14px */\r\n  }\r\n\r\n  .character-details-content p {\r\n    margin-bottom: 6px; /* Reduced spacing for mobile */\r\n    font-size: 1.1em; /* Slightly smaller than desktop but still larger than before */\r\n  }\r\n\r\n  .character-details-content p strong {\r\n    font-size: 1.2em; /* Adjusted for mobile */\r\n    margin-bottom: 1px; /* Reduced spacing between header and text */\r\n  }\r\n\r\n  /* Faster transitions on mobile */\r\n  .portrait-overlay {\r\n    transition: background-color 0.25s ease, backdrop-filter 0.25s ease;\r\n  }\r\n\r\n  .portrait-container {\r\n    transition: transform 0.25s cubic-bezier(0.175, 0.885, 0.32, 1.275), opacity 0.25s ease;\r\n  }\r\n\r\n  /* Upload overlay mobile adjustments */\r\n  .upload-title {\r\n    font-size: 1.2em;\r\n  }\r\n\r\n  .upload-description {\r\n    font-size: 1em;\r\n    margin-bottom: 20px;\r\n  }\r\n\r\n  .upload-buttons {\r\n    gap: 12px;\r\n  }\r\n\r\n  .upload-actions {\r\n    flex-direction: column;\r\n    gap: 10px;\r\n  }\r\n\r\n  .upload-select-button,\r\n  .upload-confirm-button,\r\n  .upload-cancel-button {\r\n    padding: 10px 20px;\r\n    font-size: 0.9em;\r\n    min-width: 150px;\r\n  }\r\n}\r\n\r\n/* Loading and Error Messages */\r\n.loading-message, .error-message {\r\n  text-align: center;\r\n  padding: 1rem;\r\n  margin: 1rem;\r\n  border-radius: 0.5rem;\r\n}\r\n\r\n.loading-message {\r\n  background-color: rgba(255, 255, 255, 0.1);\r\n  color: white;\r\n}\r\n\r\n.error-message {\r\n  background-color: rgba(244, 67, 54, 0.1);\r\n  color: #F44336;\r\n  border: 1px solid #F44336;\r\n}\r\n\r\n/* Responsive Adjustments */\r\n/* Small mobile devices in portrait mode */\r\n@media (max-width: 320px) {\r\n  .bingo-board {\r\n    gap: 0.3rem; /* Increased minimal gap for very small screens */\r\n    padding: 0.2rem;\r\n  }\r\n\r\n  .bingo-square {\r\n    width: 94%; /* Slightly smaller squares on very small screens */\r\n    height: 94%;\r\n  }\r\n\r\n  .bingo-square.marked::after {\r\n    font-size: 1.2rem; /* Slightly larger font size for very small screens */\r\n    color: rgba(255, 255, 255, 0.7); /* Maintain semi-transparent white */\r\n  }\r\n\r\n  .bingo-square.free.marked::after {\r\n    font-size: 2.2rem; /* Slightly larger size for very small screens */\r\n  }\r\n\r\n  /* Smaller stars for very small screens */\r\n  .rarity-stars {\r\n    bottom: 2px;\r\n    gap: 1px;\r\n  }\r\n\r\n  .star {\r\n    font-size: 14px; /* Increased from 10px */\r\n  }\r\n}\r\n\r\n/* Mobile devices in portrait mode */\r\n@media (orientation: portrait) {\r\n  .header-row {\r\n    flex-direction: column;\r\n    align-items: center;\r\n    gap: 0.5rem;\r\n    text-align: center;\r\n  }\r\n\r\n  .title-logo {\r\n    height: 65px;\r\n  }\r\n\r\n  .thank-you-message p {\r\n    font-size: 1rem;\r\n  }\r\n\r\n  .nav-container {\r\n    padding: 0.5rem 0.75rem;\r\n  }\r\n\r\n  .nav-item {\r\n    padding: 0.5rem 1rem;\r\n    font-size: 0.9rem;\r\n  }\r\n\r\n  .cards-grid {\r\n    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));\r\n    gap: 1rem;\r\n  }\r\n\r\n  .card-thumbnail {\r\n    height: 120px;\r\n  }\r\n\r\n  .points-controls-container {\r\n    margin-top: 1.5rem;\r\n    gap: 0.5rem;\r\n  }\r\n\r\n  .controls-wrapper {\r\n    gap: 0.5rem;\r\n  }\r\n\r\n  .points-display {\r\n    width: 90px;\r\n    height: 90px;\r\n    padding: 0.8rem;\r\n  }\r\n\r\n  .score-label {\r\n    font-size: 0.8rem;\r\n  }\r\n\r\n  .points-display .score-value {\r\n    font-size: 1.8rem;\r\n  }\r\n\r\n  .refresh-button {\r\n    width: 45px;\r\n    height: 45px;\r\n    font-size: 20px;\r\n  }\r\n\r\n  .points-meter-container {\r\n    height: 90px;\r\n    width: calc(100% - 160px); /* Adjusted for wrapper */\r\n  }\r\n\r\n  .points-meter {\r\n    height: 90px;\r\n  }\r\n\r\n  .progress-text {\r\n    font-size: 2rem; /* Bigger font size on mobile portrait but still fits */\r\n  }\r\n\r\n  .milestone-marker {\r\n    bottom: 0;\r\n    height: 90px;\r\n  }\r\n\r\n  .milestone-marker::before {\r\n    height: 10px;\r\n  }\r\n\r\n  .milestone-value {\r\n    font-size: 0.7rem;\r\n    bottom: -20px;\r\n  }\r\n\r\n  .rules-section, .leaderboard-section {\r\n    margin-top: 1.5rem;\r\n    padding: 1rem;\r\n  }\r\n\r\n  .leaderboard-entry {\r\n    padding: 0.4rem;\r\n    gap: 0.5rem;\r\n  }\r\n\r\n  .user-rank {\r\n    width: 1.5rem;\r\n    height: 1.5rem;\r\n    font-size: 1rem;\r\n  }\r\n\r\n  .user-name {\r\n    width: 6rem; /* Adjusted for mobile */\r\n    font-size: 1rem;\r\n  }\r\n\r\n  .score-bar-container {\r\n    height: 1.5rem;\r\n  }\r\n\r\n  .score-number {\r\n    width: 2.5rem;\r\n    font-size: 1rem;\r\n  }\r\n}\r\n\r\n/* Mobile devices in landscape mode */\r\n@media (orientation: landscape) and (max-height: 500px) {\r\n  .app-container {\r\n    padding: 0.5rem;\r\n  }\r\n\r\n  .app-header {\r\n    margin-bottom: 0.5rem;\r\n  }\r\n\r\n  .main-navigation {\r\n    margin-bottom: 1rem;\r\n  }\r\n\r\n  .title-logo {\r\n    height: 55px;\r\n  }\r\n\r\n  .thank-you-message p {\r\n    font-size: 0.9rem;\r\n  }\r\n\r\n  .nav-container {\r\n    padding: 0.5rem 0.75rem;\r\n  }\r\n\r\n  .nav-item {\r\n    padding: 0.4rem 0.8rem;\r\n    font-size: 0.8rem;\r\n  }\r\n\r\n  .bingo-board {\r\n    max-height: 80vh; /* Ensure it fits in landscape mode */\r\n  }\r\n\r\n  .points-controls-container {\r\n    margin-top: 1rem;\r\n    gap: 0.5rem;\r\n  }\r\n\r\n  .controls-wrapper {\r\n    gap: 0.4rem;\r\n  }\r\n\r\n  .points-display {\r\n    width: 80px;\r\n    height: 80px;\r\n    padding: 0.6rem;\r\n  }\r\n\r\n  .score-label {\r\n    font-size: 0.7rem;\r\n    margin-bottom: 0.2rem;\r\n  }\r\n\r\n  .points-display .score-value {\r\n    font-size: 1.6rem;\r\n  }\r\n\r\n  .refresh-button {\r\n    width: 40px;\r\n    height: 40px;\r\n    font-size: 18px;\r\n  }\r\n\r\n  .points-meter-container {\r\n    height: 80px;\r\n    width: calc(100% - 145px); /* Adjusted for wrapper */\r\n  }\r\n\r\n  .points-meter {\r\n    height: 80px;\r\n  }\r\n\r\n  .progress-text {\r\n    font-size: 1.8rem; /* Bigger font size on mobile landscape but still fits */\r\n  }\r\n\r\n  .milestone-marker {\r\n    bottom: 0;\r\n    height: 80px;\r\n  }\r\n\r\n  .milestone-marker::before {\r\n    height: 10px;\r\n  }\r\n\r\n  .milestone-value {\r\n    font-size: 0.65rem;\r\n    bottom: -20px;\r\n  }\r\n\r\n  .rules-section, .leaderboard-section {\r\n    margin-top: 1rem;\r\n    padding: 0.8rem;\r\n  }\r\n\r\n  .leaderboard-container {\r\n    gap: 0.5rem;\r\n  }\r\n\r\n  .leaderboard-entry {\r\n    padding: 0.3rem;\r\n    gap: 0.5rem;\r\n  }\r\n\r\n  .user-rank {\r\n    width: 1.5rem;\r\n    height: 1.5rem;\r\n    font-size: 0.9rem;\r\n  }\r\n\r\n  .user-name {\r\n    width: 5rem; /* Adjusted for mobile landscape */\r\n    font-size: 0.9rem;\r\n  }\r\n\r\n  .score-bar-container {\r\n    height: 1.5rem;\r\n  }\r\n\r\n  .score-number {\r\n    width: 2rem;\r\n    font-size: 0.9rem;\r\n  }\r\n}\r\n\r\n/* Tablets and medium-sized screens */\r\n@media (min-width: 768px) and (min-height: 768px) {\r\n  .bingo-board {\r\n    gap: min(1.5vw, 0.6rem); /* Increased gap for better visibility on tablets */\r\n    padding: min(1.5vw, 0.6rem);\r\n  }\r\n\r\n  .bingo-square {\r\n    width: 95%;\r\n    height: 95%;\r\n    border: min(0.6vw, 4px) solid;\r\n  }\r\n\r\n  .bingo-square.marked::after {\r\n    font-size: 2rem; /* Increased font size for tablets */\r\n    color: rgba(255, 255, 255, 0.7); /* Maintain semi-transparent white */\r\n  }\r\n\r\n  .bingo-square.free.marked::after {\r\n    font-size: 2.8rem; /* Increased size for tablets */\r\n  }\r\n\r\n  /* Larger stars for tablets */\r\n  .rarity-stars {\r\n    bottom: 8px;\r\n    gap: 3px;\r\n  }\r\n\r\n  .star {\r\n    font-size: 24px; /* Increased from 18px */\r\n  }\r\n}\r\n\r\n/* Large screens and desktops */\r\n@media (min-width: 1200px) {\r\n  .bingo-board {\r\n    max-width: 80vh; /* Slightly smaller on large screens for better aesthetics */\r\n    max-height: 80vh;\r\n    gap: 0.7rem; /* Fixed gap size for large screens */\r\n  }\r\n\r\n  .bingo-square {\r\n    width: 95%;\r\n    height: 95%;\r\n    border: 4px solid;\r\n  }\r\n\r\n  .bingo-square.marked::after {\r\n    font-size: 2.2rem; /* Increased font size for desktops */\r\n    color: rgba(255, 255, 255, 0.7); /* Maintain semi-transparent white */\r\n  }\r\n\r\n  .bingo-square.free.marked::after {\r\n    font-size: 3.5rem; /* Increased size for desktops */\r\n  }\r\n\r\n  /* Larger stars for big screens */\r\n  .rarity-stars {\r\n    bottom: 10px;\r\n    gap: 4px;\r\n  }\r\n\r\n  .star {\r\n    font-size: 28px; /* Increased from 22px */\r\n    filter: drop-shadow(0 0 3px rgba(0, 0, 0, 0.8));\r\n  }\r\n}\r\n"], "names": [], "sourceRoot": ""}
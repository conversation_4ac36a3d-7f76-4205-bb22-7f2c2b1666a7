{"ast": null, "code": "import React,{useState,useEffect,useRef}from'react';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const PortraitOverlay=_ref=>{let{character,onClose,onClaim,sourcePosition,isClaimed,isReadOnly,userId,boardId,squareIndex,userImage}=_ref;// Get the portrait URL from the portrait path\nconst getPortraitUrl=portraitPath=>{if(!portraitPath)return null;return\"\".concat(process.env.PUBLIC_URL).concat(portraitPath);};// Use user image if available and claimed, otherwise use original portrait\nconst portraitUrl=isClaimed&&userImage?\"\".concat(process.env.PUBLIC_URL).concat(userImage):getPortraitUrl(character.Portrait);// Get the frame overlay URL based on character rarity\nconst getFrameOverlayUrl=rarity=>{if(!rarity||rarity==='FREE')return null;// No frame for FREE characters\nreturn\"\".concat(process.env.PUBLIC_URL,\"/frames/\").concat(rarity,\" - Portrait.png\");};// Only show frame overlay for character portraits, not user images\nconst frameOverlayUrl=isClaimed&&userImage?null:getFrameOverlayUrl(character.rarity);// Use state to control animation classes and details visibility\nconst[isVisible,setIsVisible]=useState(false);const[showDetails,setShowDetails]=useState(false);const[selectedFile,setSelectedFile]=useState(null);const[uploading,setUploading]=useState(false);const[showUpload,setShowUpload]=useState(false);const fileInputRef=useRef(null);// Apply the animation after component mounts\nuseEffect(()=>{// Small delay to ensure the component is rendered before animation starts\nconst timer=setTimeout(()=>{setIsVisible(true);},50);return()=>clearTimeout(timer);},[]);// Handle closing with animation\nconst handleClose=()=>{setIsVisible(false);setShowDetails(false);// Wait for animation to complete before actually closing\nsetTimeout(onClose,300);};// Handle file selection\nconst handleFileSelect=event=>{const file=event.target.files[0];if(file){setSelectedFile(file);setShowUpload(true);}};// Handle file upload\nconst handleUpload=async()=>{if(!selectedFile||!userId||!boardId||squareIndex===undefined)return;setUploading(true);const formData=new FormData();formData.append('file',selectedFile);try{const response=await fetch(\"http://localhost:5000/api/users/\".concat(userId,\"/boards/\").concat(boardId,\"/upload/\").concat(squareIndex),{method:'POST',body:formData});if(response.ok){const result=await response.json();// Call onClaim with the uploaded image path\nonClaim(result.image_path);setIsVisible(false);setShowDetails(false);setShowUpload(false);}else{console.error('Upload failed');alert('Failed to upload image. Please try again.');}}catch(error){console.error('Upload error:',error);alert('Failed to upload image. Please try again.');}finally{setUploading(false);}};// Handle claiming with animation (for unclaiming)\nconst handleClaim=()=>{if(isReadOnly)return;// Disable claiming for read-only mode\nif(isClaimed){// If already claimed, just unclaim\nsetIsVisible(false);setShowDetails(false);setTimeout(()=>onClaim(null),300);}else{// If not claimed, show upload interface\nsetShowUpload(true);}};// Handle showing/hiding details\nconst handleToggleDetails=()=>{setShowDetails(!showDetails);};// Get rarity value text\nconst getRarityValue=rarity=>{switch(rarity){case'FREE':return'FREE (1 pt)';case'R':return'R (2 pts)';case'SR':return'SR (3 pts)';case'SSR':return'SSR (4 pts)';case'UR+':return'UR+ (6 pts)';default:return'Unknown';}};// Get rarity color for details button\nconst getRarityColor=rarity=>{switch(rarity){case'FREE':return'#4CAF50';// Green\ncase'R':return'#2196F3';// Blue\ncase'SR':return'#9C27B0';// Purple\ncase'SSR':return'#FF9800';// Orange\ncase'UR+':return'#F44336';// Red\ndefault:return'#2196F3';// Default blue\n}};return/*#__PURE__*/_jsx(\"div\",{className:\"portrait-overlay \".concat(isVisible?'visible':''),children:/*#__PURE__*/_jsxs(\"div\",{className:\"portrait-container \".concat(isVisible?'visible':''),style:sourcePosition?{// If we have source position, use it for initial transform origin\ntransformOrigin:\"\".concat(sourcePosition.x,\"px \").concat(sourcePosition.y,\"px\")}:{},children:[/*#__PURE__*/_jsx(\"button\",{className:\"close-button\",onClick:handleClose,children:\"\\xD7\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"portrait-image-container\",children:[/*#__PURE__*/_jsx(\"img\",{src:portraitUrl,alt:character.Name,className:\"\".concat(isClaimed&&userImage?'user-uploaded-portrait':'character-portrait')}),frameOverlayUrl&&/*#__PURE__*/_jsx(\"img\",{src:frameOverlayUrl,alt:\"\".concat(character.rarity,\" frame\"),className:\"portrait-frame-overlay\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"portrait-buttons\",children:[!isReadOnly&&!showUpload&&/*#__PURE__*/_jsx(\"button\",{className:\"claim-button \".concat(isClaimed?'unclaim':''),onClick:handleClaim,children:isClaimed?'Unclaim':'Claim!'}),!showUpload&&/*#__PURE__*/_jsx(\"button\",{className:\"details-button\",onClick:handleToggleDetails,style:{backgroundColor:getRarityColor(character.rarity),boxShadow:\"0 0 20px \".concat(getRarityColor(character.rarity),\"80, 0 4px 6px rgba(0, 0, 0, 0.1)\")},children:\"Details\"})]}),showUpload&&!isReadOnly&&/*#__PURE__*/_jsx(\"div\",{className:\"character-details-overlay\",onClick:()=>{setShowUpload(false);setSelectedFile(null);},children:/*#__PURE__*/_jsxs(\"div\",{className:\"character-details-content upload-overlay-content\",onClick:e=>e.stopPropagation()// Prevent clicks on content from closing\n,children:[/*#__PURE__*/_jsx(\"h3\",{className:\"upload-title\",children:\"Upload Your Photo\"}),/*#__PURE__*/_jsx(\"p\",{className:\"upload-description\",children:\"Upload a photo of yourself with this cosplayer to claim this square!\"}),/*#__PURE__*/_jsx(\"input\",{type:\"file\",ref:fileInputRef,onChange:handleFileSelect,accept:\"image/*\",style:{display:'none'}}),/*#__PURE__*/_jsxs(\"div\",{className:\"upload-buttons\",children:[/*#__PURE__*/_jsx(\"button\",{className:\"details-button upload-select-button\",onClick:()=>{var _fileInputRef$current;return(_fileInputRef$current=fileInputRef.current)===null||_fileInputRef$current===void 0?void 0:_fileInputRef$current.click();},disabled:uploading,style:{backgroundColor:getRarityColor(character.rarity),boxShadow:\"0 0 20px \".concat(getRarityColor(character.rarity),\"80, 0 4px 6px rgba(0, 0, 0, 0.1)\")},children:selectedFile?'Change Photo':'Select Photo'}),selectedFile&&/*#__PURE__*/_jsxs(\"div\",{className:\"selected-file-info\",children:[/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{style:{color:getRarityColor(character.rarity)},children:\"Selected:\"}),/*#__PURE__*/_jsx(\"br\",{}),selectedFile.name]}),/*#__PURE__*/_jsxs(\"div\",{className:\"upload-actions\",children:[/*#__PURE__*/_jsx(\"button\",{className:\"details-button upload-confirm-button\",onClick:handleUpload,disabled:uploading,style:{backgroundColor:'#4CAF50',boxShadow:'0 0 20px #4CAF5080, 0 4px 6px rgba(0, 0, 0, 0.1)'},children:uploading?'Uploading...':'Upload & Claim'}),/*#__PURE__*/_jsx(\"button\",{className:\"details-button upload-cancel-button\",onClick:()=>{setShowUpload(false);setSelectedFile(null);},disabled:uploading,style:{backgroundColor:'#f44336',boxShadow:'0 0 20px #f4433680, 0 4px 6px rgba(0, 0, 0, 0.1)'},children:\"Cancel\"})]})]}),!selectedFile&&/*#__PURE__*/_jsx(\"button\",{className:\"details-button upload-cancel-button\",onClick:()=>{setShowUpload(false);setSelectedFile(null);},disabled:uploading,style:{backgroundColor:'#f44336',boxShadow:'0 0 20px #f4433680, 0 4px 6px rgba(0, 0, 0, 0.1)'},children:\"Cancel\"})]})]})}),showDetails&&/*#__PURE__*/_jsx(\"div\",{className:\"character-details-overlay\",onClick:()=>setShowDetails(false),children:/*#__PURE__*/_jsxs(\"div\",{className:\"character-details-content\",onClick:e=>e.stopPropagation()// Prevent clicks on content from closing\n,children:[/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{style:{color:getRarityColor(character.rarity)},children:\"Name:\"}),/*#__PURE__*/_jsx(\"br\",{}),character.Name]}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{style:{color:getRarityColor(character.rarity)},children:\"Source:\"}),/*#__PURE__*/_jsx(\"br\",{}),character.Source]}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{style:{color:getRarityColor(character.rarity)},children:\"Value:\"}),/*#__PURE__*/_jsx(\"br\",{}),getRarityValue(character.rarity)]}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{style:{color:getRarityColor(character.rarity)},children:\"What to look for:\"}),/*#__PURE__*/_jsx(\"br\",{}),character.description||\"No description available\"]}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{style:{color:getRarityColor(character.rarity)},children:\"Special conditions:\"}),/*#__PURE__*/_jsx(\"br\",{}),character.conditions||\"None\"]})]})})]})});};export default PortraitOverlay;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "jsx", "_jsx", "jsxs", "_jsxs", "PortraitOverlay", "_ref", "character", "onClose", "onClaim", "sourcePosition", "isClaimed", "isReadOnly", "userId", "boardId", "squareIndex", "userImage", "getPortraitUrl", "<PERSON><PERSON><PERSON>", "concat", "process", "env", "PUBLIC_URL", "portraitUrl", "Portrait", "getFrameOverlayUrl", "rarity", "frameOverlayUrl", "isVisible", "setIsVisible", "showDetails", "setShowDetails", "selectedFile", "setSelectedFile", "uploading", "setUploading", "showUpload", "setShowUpload", "fileInputRef", "timer", "setTimeout", "clearTimeout", "handleClose", "handleFileSelect", "event", "file", "target", "files", "handleUpload", "undefined", "formData", "FormData", "append", "response", "fetch", "method", "body", "ok", "result", "json", "image_path", "console", "error", "alert", "handleClaim", "handleToggleDetails", "getRarityValue", "getRarityColor", "className", "children", "style", "transform<PERSON><PERSON>in", "x", "y", "onClick", "src", "alt", "Name", "backgroundColor", "boxShadow", "e", "stopPropagation", "type", "ref", "onChange", "accept", "display", "_fileInputRef$current", "current", "click", "disabled", "color", "name", "Source", "description", "conditions"], "sources": ["C:/Users/<USER>/Documents/Projects/bimbo-hunter-base/client/src/components/Board/PortraitOverlay.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\r\n\r\nconst PortraitOverlay = ({ character, onClose, onClaim, sourcePosition, isClaimed, isReadOnly, userId, boardId, squareIndex, userImage }) => {\r\n  // Get the portrait URL from the portrait path\r\n  const getPortraitUrl = (portraitPath) => {\r\n    if (!portraitPath) return null;\r\n    return `${process.env.PUBLIC_URL}${portraitPath}`;\r\n  };\r\n\r\n  // Use user image if available and claimed, otherwise use original portrait\r\n  const portraitUrl = (isClaimed && userImage)\r\n    ? `${process.env.PUBLIC_URL}${userImage}`\r\n    : getPortraitUrl(character.Portrait);\r\n\r\n  // Get the frame overlay URL based on character rarity\r\n  const getFrameOverlayUrl = (rarity) => {\r\n    if (!rarity || rarity === 'FREE') return null; // No frame for FREE characters\r\n    return `${process.env.PUBLIC_URL}/frames/${rarity} - Portrait.png`;\r\n  };\r\n\r\n  // Only show frame overlay for character portraits, not user images\r\n  const frameOverlayUrl = (isClaimed && userImage) ? null : getFrameOverlayUrl(character.rarity);\r\n\r\n  // Use state to control animation classes and details visibility\r\n  const [isVisible, setIsVisible] = useState(false);\r\n  const [showDetails, setShowDetails] = useState(false);\r\n  const [selectedFile, setSelectedFile] = useState(null);\r\n  const [uploading, setUploading] = useState(false);\r\n  const [showUpload, setShowUpload] = useState(false);\r\n  const fileInputRef = useRef(null);\r\n\r\n  // Apply the animation after component mounts\r\n  useEffect(() => {\r\n    // Small delay to ensure the component is rendered before animation starts\r\n    const timer = setTimeout(() => {\r\n      setIsVisible(true);\r\n    }, 50);\r\n\r\n    return () => clearTimeout(timer);\r\n  }, []);\r\n\r\n  // Handle closing with animation\r\n  const handleClose = () => {\r\n    setIsVisible(false);\r\n    setShowDetails(false);\r\n    // Wait for animation to complete before actually closing\r\n    setTimeout(onClose, 300);\r\n  };\r\n\r\n  // Handle file selection\r\n  const handleFileSelect = (event) => {\r\n    const file = event.target.files[0];\r\n    if (file) {\r\n      setSelectedFile(file);\r\n      setShowUpload(true);\r\n    }\r\n  };\r\n\r\n  // Handle file upload\r\n  const handleUpload = async () => {\r\n    if (!selectedFile || !userId || !boardId || squareIndex === undefined) return;\r\n\r\n    setUploading(true);\r\n    const formData = new FormData();\r\n    formData.append('file', selectedFile);\r\n\r\n    try {\r\n      const response = await fetch(`http://localhost:5000/api/users/${userId}/boards/${boardId}/upload/${squareIndex}`, {\r\n        method: 'POST',\r\n        body: formData,\r\n      });\r\n\r\n      if (response.ok) {\r\n        const result = await response.json();\r\n        // Call onClaim with the uploaded image path\r\n        onClaim(result.image_path);\r\n        setIsVisible(false);\r\n        setShowDetails(false);\r\n        setShowUpload(false);\r\n      } else {\r\n        console.error('Upload failed');\r\n        alert('Failed to upload image. Please try again.');\r\n      }\r\n    } catch (error) {\r\n      console.error('Upload error:', error);\r\n      alert('Failed to upload image. Please try again.');\r\n    } finally {\r\n      setUploading(false);\r\n    }\r\n  };\r\n\r\n  // Handle claiming with animation (for unclaiming)\r\n  const handleClaim = () => {\r\n    if (isReadOnly) return; // Disable claiming for read-only mode\r\n\r\n    if (isClaimed) {\r\n      // If already claimed, just unclaim\r\n      setIsVisible(false);\r\n      setShowDetails(false);\r\n      setTimeout(() => onClaim(null), 300);\r\n    } else {\r\n      // If not claimed, show upload interface\r\n      setShowUpload(true);\r\n    }\r\n  };\r\n\r\n  // Handle showing/hiding details\r\n  const handleToggleDetails = () => {\r\n    setShowDetails(!showDetails);\r\n  };\r\n\r\n  // Get rarity value text\r\n  const getRarityValue = (rarity) => {\r\n    switch (rarity) {\r\n      case 'FREE':\r\n        return 'FREE (1 pt)';\r\n      case 'R':\r\n        return 'R (2 pts)';\r\n      case 'SR':\r\n        return 'SR (3 pts)';\r\n      case 'SSR':\r\n        return 'SSR (4 pts)';\r\n      case 'UR+':\r\n        return 'UR+ (6 pts)';\r\n      default:\r\n        return 'Unknown';\r\n    }\r\n  };\r\n\r\n  // Get rarity color for details button\r\n  const getRarityColor = (rarity) => {\r\n    switch (rarity) {\r\n      case 'FREE':\r\n        return '#4CAF50'; // Green\r\n      case 'R':\r\n        return '#2196F3'; // Blue\r\n      case 'SR':\r\n        return '#9C27B0'; // Purple\r\n      case 'SSR':\r\n        return '#FF9800'; // Orange\r\n      case 'UR+':\r\n        return '#F44336'; // Red\r\n      default:\r\n        return '#2196F3'; // Default blue\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className={`portrait-overlay ${isVisible ? 'visible' : ''}`}>\r\n      <div\r\n        className={`portrait-container ${isVisible ? 'visible' : ''}`}\r\n        style={sourcePosition ? {\r\n          // If we have source position, use it for initial transform origin\r\n          transformOrigin: `${sourcePosition.x}px ${sourcePosition.y}px`\r\n        } : {}}\r\n      >\r\n        <button className=\"close-button\" onClick={handleClose}>×</button>\r\n        <div className=\"portrait-image-container\">\r\n          <img\r\n            src={portraitUrl}\r\n            alt={character.Name}\r\n            className={`${(isClaimed && userImage) ? 'user-uploaded-portrait' : 'character-portrait'}`}\r\n          />\r\n          {frameOverlayUrl && (\r\n            <img\r\n              src={frameOverlayUrl}\r\n              alt={`${character.rarity} frame`}\r\n              className=\"portrait-frame-overlay\"\r\n            />\r\n          )}\r\n        </div>\r\n        <div className=\"portrait-buttons\">\r\n          {!isReadOnly && !showUpload && (\r\n            <button\r\n              className={`claim-button ${isClaimed ? 'unclaim' : ''}`}\r\n              onClick={handleClaim}\r\n            >\r\n              {isClaimed ? 'Unclaim' : 'Claim!'}\r\n            </button>\r\n          )}\r\n          {!showUpload && (\r\n            <button\r\n              className=\"details-button\"\r\n              onClick={handleToggleDetails}\r\n              style={{\r\n                backgroundColor: getRarityColor(character.rarity),\r\n                boxShadow: `0 0 20px ${getRarityColor(character.rarity)}80, 0 4px 6px rgba(0, 0, 0, 0.1)`\r\n              }}\r\n            >\r\n              Details\r\n            </button>\r\n          )}\r\n        </div>\r\n\r\n        {/* Upload Interface Overlay */}\r\n        {showUpload && !isReadOnly && (\r\n          <div\r\n            className=\"character-details-overlay\"\r\n            onClick={() => {\r\n              setShowUpload(false);\r\n              setSelectedFile(null);\r\n            }}\r\n          >\r\n            <div\r\n              className=\"character-details-content upload-overlay-content\"\r\n              onClick={(e) => e.stopPropagation()} // Prevent clicks on content from closing\r\n            >\r\n              <h3 className=\"upload-title\">Upload Your Photo</h3>\r\n              <p className=\"upload-description\">Upload a photo of yourself with this cosplayer to claim this square!</p>\r\n\r\n              <input\r\n                type=\"file\"\r\n                ref={fileInputRef}\r\n                onChange={handleFileSelect}\r\n                accept=\"image/*\"\r\n                style={{ display: 'none' }}\r\n              />\r\n\r\n              <div className=\"upload-buttons\">\r\n                <button\r\n                  className=\"details-button upload-select-button\"\r\n                  onClick={() => fileInputRef.current?.click()}\r\n                  disabled={uploading}\r\n                  style={{\r\n                    backgroundColor: getRarityColor(character.rarity),\r\n                    boxShadow: `0 0 20px ${getRarityColor(character.rarity)}80, 0 4px 6px rgba(0, 0, 0, 0.1)`\r\n                  }}\r\n                >\r\n                  {selectedFile ? 'Change Photo' : 'Select Photo'}\r\n                </button>\r\n\r\n                {selectedFile && (\r\n                  <div className=\"selected-file-info\">\r\n                    <p><strong style={{ color: getRarityColor(character.rarity) }}>Selected:</strong><br />{selectedFile.name}</p>\r\n                    <div className=\"upload-actions\">\r\n                      <button\r\n                        className=\"details-button upload-confirm-button\"\r\n                        onClick={handleUpload}\r\n                        disabled={uploading}\r\n                        style={{\r\n                          backgroundColor: '#4CAF50',\r\n                          boxShadow: '0 0 20px #4CAF5080, 0 4px 6px rgba(0, 0, 0, 0.1)'\r\n                        }}\r\n                      >\r\n                        {uploading ? 'Uploading...' : 'Upload & Claim'}\r\n                      </button>\r\n                      <button\r\n                        className=\"details-button upload-cancel-button\"\r\n                        onClick={() => {\r\n                          setShowUpload(false);\r\n                          setSelectedFile(null);\r\n                        }}\r\n                        disabled={uploading}\r\n                        style={{\r\n                          backgroundColor: '#f44336',\r\n                          boxShadow: '0 0 20px #f4433680, 0 4px 6px rgba(0, 0, 0, 0.1)'\r\n                        }}\r\n                      >\r\n                        Cancel\r\n                      </button>\r\n                    </div>\r\n                  </div>\r\n                )}\r\n\r\n                {/* Cancel button always visible */}\r\n                {!selectedFile && (\r\n                  <button\r\n                    className=\"details-button upload-cancel-button\"\r\n                    onClick={() => {\r\n                      setShowUpload(false);\r\n                      setSelectedFile(null);\r\n                    }}\r\n                    disabled={uploading}\r\n                    style={{\r\n                      backgroundColor: '#f44336',\r\n                      boxShadow: '0 0 20px #f4433680, 0 4px 6px rgba(0, 0, 0, 0.1)'\r\n                    }}\r\n                  >\r\n                    Cancel\r\n                  </button>\r\n                )}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {showDetails && (\r\n          <div\r\n            className=\"character-details-overlay\"\r\n            onClick={() => setShowDetails(false)}\r\n          >\r\n            <div\r\n              className=\"character-details-content\"\r\n              onClick={(e) => e.stopPropagation()} // Prevent clicks on content from closing\r\n            >\r\n              <p><strong style={{ color: getRarityColor(character.rarity) }}>Name:</strong><br />{character.Name}</p>\r\n              <p><strong style={{ color: getRarityColor(character.rarity) }}>Source:</strong><br />{character.Source}</p>\r\n              <p><strong style={{ color: getRarityColor(character.rarity) }}>Value:</strong><br />{getRarityValue(character.rarity)}</p>\r\n              <p><strong style={{ color: getRarityColor(character.rarity) }}>What to look for:</strong><br />{character.description || \"No description available\"}</p>\r\n              <p><strong style={{ color: getRarityColor(character.rarity) }}>Special conditions:</strong><br />{character.conditions || \"None\"}</p>\r\n            </div>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default PortraitOverlay;\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,CAAEC,MAAM,KAAQ,OAAO,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE3D,KAAM,CAAAC,eAAe,CAAGC,IAAA,EAAqH,IAApH,CAAEC,SAAS,CAAEC,OAAO,CAAEC,OAAO,CAAEC,cAAc,CAAEC,SAAS,CAAEC,UAAU,CAAEC,MAAM,CAAEC,OAAO,CAAEC,WAAW,CAAEC,SAAU,CAAC,CAAAV,IAAA,CACtI;AACA,KAAM,CAAAW,cAAc,CAAIC,YAAY,EAAK,CACvC,GAAI,CAACA,YAAY,CAAE,MAAO,KAAI,CAC9B,SAAAC,MAAA,CAAUC,OAAO,CAACC,GAAG,CAACC,UAAU,EAAAH,MAAA,CAAGD,YAAY,EACjD,CAAC,CAED;AACA,KAAM,CAAAK,WAAW,CAAIZ,SAAS,EAAIK,SAAS,IAAAG,MAAA,CACpCC,OAAO,CAACC,GAAG,CAACC,UAAU,EAAAH,MAAA,CAAGH,SAAS,EACrCC,cAAc,CAACV,SAAS,CAACiB,QAAQ,CAAC,CAEtC;AACA,KAAM,CAAAC,kBAAkB,CAAIC,MAAM,EAAK,CACrC,GAAI,CAACA,MAAM,EAAIA,MAAM,GAAK,MAAM,CAAE,MAAO,KAAI,CAAE;AAC/C,SAAAP,MAAA,CAAUC,OAAO,CAACC,GAAG,CAACC,UAAU,aAAAH,MAAA,CAAWO,MAAM,oBACnD,CAAC,CAED;AACA,KAAM,CAAAC,eAAe,CAAIhB,SAAS,EAAIK,SAAS,CAAI,IAAI,CAAGS,kBAAkB,CAAClB,SAAS,CAACmB,MAAM,CAAC,CAE9F;AACA,KAAM,CAACE,SAAS,CAAEC,YAAY,CAAC,CAAG/B,QAAQ,CAAC,KAAK,CAAC,CACjD,KAAM,CAACgC,WAAW,CAAEC,cAAc,CAAC,CAAGjC,QAAQ,CAAC,KAAK,CAAC,CACrD,KAAM,CAACkC,YAAY,CAAEC,eAAe,CAAC,CAAGnC,QAAQ,CAAC,IAAI,CAAC,CACtD,KAAM,CAACoC,SAAS,CAAEC,YAAY,CAAC,CAAGrC,QAAQ,CAAC,KAAK,CAAC,CACjD,KAAM,CAACsC,UAAU,CAAEC,aAAa,CAAC,CAAGvC,QAAQ,CAAC,KAAK,CAAC,CACnD,KAAM,CAAAwC,YAAY,CAAGtC,MAAM,CAAC,IAAI,CAAC,CAEjC;AACAD,SAAS,CAAC,IAAM,CACd;AACA,KAAM,CAAAwC,KAAK,CAAGC,UAAU,CAAC,IAAM,CAC7BX,YAAY,CAAC,IAAI,CAAC,CACpB,CAAC,CAAE,EAAE,CAAC,CAEN,MAAO,IAAMY,YAAY,CAACF,KAAK,CAAC,CAClC,CAAC,CAAE,EAAE,CAAC,CAEN;AACA,KAAM,CAAAG,WAAW,CAAGA,CAAA,GAAM,CACxBb,YAAY,CAAC,KAAK,CAAC,CACnBE,cAAc,CAAC,KAAK,CAAC,CACrB;AACAS,UAAU,CAAChC,OAAO,CAAE,GAAG,CAAC,CAC1B,CAAC,CAED;AACA,KAAM,CAAAmC,gBAAgB,CAAIC,KAAK,EAAK,CAClC,KAAM,CAAAC,IAAI,CAAGD,KAAK,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,CAClC,GAAIF,IAAI,CAAE,CACRZ,eAAe,CAACY,IAAI,CAAC,CACrBR,aAAa,CAAC,IAAI,CAAC,CACrB,CACF,CAAC,CAED;AACA,KAAM,CAAAW,YAAY,CAAG,KAAAA,CAAA,GAAY,CAC/B,GAAI,CAAChB,YAAY,EAAI,CAACnB,MAAM,EAAI,CAACC,OAAO,EAAIC,WAAW,GAAKkC,SAAS,CAAE,OAEvEd,YAAY,CAAC,IAAI,CAAC,CAClB,KAAM,CAAAe,QAAQ,CAAG,GAAI,CAAAC,QAAQ,CAAC,CAAC,CAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,CAAEpB,YAAY,CAAC,CAErC,GAAI,CACF,KAAM,CAAAqB,QAAQ,CAAG,KAAM,CAAAC,KAAK,oCAAAnC,MAAA,CAAoCN,MAAM,aAAAM,MAAA,CAAWL,OAAO,aAAAK,MAAA,CAAWJ,WAAW,EAAI,CAChHwC,MAAM,CAAE,MAAM,CACdC,IAAI,CAAEN,QACR,CAAC,CAAC,CAEF,GAAIG,QAAQ,CAACI,EAAE,CAAE,CACf,KAAM,CAAAC,MAAM,CAAG,KAAM,CAAAL,QAAQ,CAACM,IAAI,CAAC,CAAC,CACpC;AACAlD,OAAO,CAACiD,MAAM,CAACE,UAAU,CAAC,CAC1B/B,YAAY,CAAC,KAAK,CAAC,CACnBE,cAAc,CAAC,KAAK,CAAC,CACrBM,aAAa,CAAC,KAAK,CAAC,CACtB,CAAC,IAAM,CACLwB,OAAO,CAACC,KAAK,CAAC,eAAe,CAAC,CAC9BC,KAAK,CAAC,2CAA2C,CAAC,CACpD,CACF,CAAE,MAAOD,KAAK,CAAE,CACdD,OAAO,CAACC,KAAK,CAAC,eAAe,CAAEA,KAAK,CAAC,CACrCC,KAAK,CAAC,2CAA2C,CAAC,CACpD,CAAC,OAAS,CACR5B,YAAY,CAAC,KAAK,CAAC,CACrB,CACF,CAAC,CAED;AACA,KAAM,CAAA6B,WAAW,CAAGA,CAAA,GAAM,CACxB,GAAIpD,UAAU,CAAE,OAAQ;AAExB,GAAID,SAAS,CAAE,CACb;AACAkB,YAAY,CAAC,KAAK,CAAC,CACnBE,cAAc,CAAC,KAAK,CAAC,CACrBS,UAAU,CAAC,IAAM/B,OAAO,CAAC,IAAI,CAAC,CAAE,GAAG,CAAC,CACtC,CAAC,IAAM,CACL;AACA4B,aAAa,CAAC,IAAI,CAAC,CACrB,CACF,CAAC,CAED;AACA,KAAM,CAAA4B,mBAAmB,CAAGA,CAAA,GAAM,CAChClC,cAAc,CAAC,CAACD,WAAW,CAAC,CAC9B,CAAC,CAED;AACA,KAAM,CAAAoC,cAAc,CAAIxC,MAAM,EAAK,CACjC,OAAQA,MAAM,EACZ,IAAK,MAAM,CACT,MAAO,aAAa,CACtB,IAAK,GAAG,CACN,MAAO,WAAW,CACpB,IAAK,IAAI,CACP,MAAO,YAAY,CACrB,IAAK,KAAK,CACR,MAAO,aAAa,CACtB,IAAK,KAAK,CACR,MAAO,aAAa,CACtB,QACE,MAAO,SAAS,CACpB,CACF,CAAC,CAED;AACA,KAAM,CAAAyC,cAAc,CAAIzC,MAAM,EAAK,CACjC,OAAQA,MAAM,EACZ,IAAK,MAAM,CACT,MAAO,SAAS,CAAE;AACpB,IAAK,GAAG,CACN,MAAO,SAAS,CAAE;AACpB,IAAK,IAAI,CACP,MAAO,SAAS,CAAE;AACpB,IAAK,KAAK,CACR,MAAO,SAAS,CAAE;AACpB,IAAK,KAAK,CACR,MAAO,SAAS,CAAE;AACpB,QACE,MAAO,SAAS,CAAE;AACtB,CACF,CAAC,CAED,mBACExB,IAAA,QAAKkE,SAAS,qBAAAjD,MAAA,CAAsBS,SAAS,CAAG,SAAS,CAAG,EAAE,CAAG,CAAAyC,QAAA,cAC/DjE,KAAA,QACEgE,SAAS,uBAAAjD,MAAA,CAAwBS,SAAS,CAAG,SAAS,CAAG,EAAE,CAAG,CAC9D0C,KAAK,CAAE5D,cAAc,CAAG,CACtB;AACA6D,eAAe,IAAApD,MAAA,CAAKT,cAAc,CAAC8D,CAAC,QAAArD,MAAA,CAAMT,cAAc,CAAC+D,CAAC,MAC5D,CAAC,CAAG,CAAC,CAAE,CAAAJ,QAAA,eAEPnE,IAAA,WAAQkE,SAAS,CAAC,cAAc,CAACM,OAAO,CAAEhC,WAAY,CAAA2B,QAAA,CAAC,MAAC,CAAQ,CAAC,cACjEjE,KAAA,QAAKgE,SAAS,CAAC,0BAA0B,CAAAC,QAAA,eACvCnE,IAAA,QACEyE,GAAG,CAAEpD,WAAY,CACjBqD,GAAG,CAAErE,SAAS,CAACsE,IAAK,CACpBT,SAAS,IAAAjD,MAAA,CAAMR,SAAS,EAAIK,SAAS,CAAI,wBAAwB,CAAG,oBAAoB,CAAG,CAC5F,CAAC,CACDW,eAAe,eACdzB,IAAA,QACEyE,GAAG,CAAEhD,eAAgB,CACrBiD,GAAG,IAAAzD,MAAA,CAAKZ,SAAS,CAACmB,MAAM,UAAS,CACjC0C,SAAS,CAAC,wBAAwB,CACnC,CACF,EACE,CAAC,cACNhE,KAAA,QAAKgE,SAAS,CAAC,kBAAkB,CAAAC,QAAA,EAC9B,CAACzD,UAAU,EAAI,CAACwB,UAAU,eACzBlC,IAAA,WACEkE,SAAS,iBAAAjD,MAAA,CAAkBR,SAAS,CAAG,SAAS,CAAG,EAAE,CAAG,CACxD+D,OAAO,CAAEV,WAAY,CAAAK,QAAA,CAEpB1D,SAAS,CAAG,SAAS,CAAG,QAAQ,CAC3B,CACT,CACA,CAACyB,UAAU,eACVlC,IAAA,WACEkE,SAAS,CAAC,gBAAgB,CAC1BM,OAAO,CAAET,mBAAoB,CAC7BK,KAAK,CAAE,CACLQ,eAAe,CAAEX,cAAc,CAAC5D,SAAS,CAACmB,MAAM,CAAC,CACjDqD,SAAS,aAAA5D,MAAA,CAAcgD,cAAc,CAAC5D,SAAS,CAACmB,MAAM,CAAC,oCACzD,CAAE,CAAA2C,QAAA,CACH,SAED,CAAQ,CACT,EACE,CAAC,CAGLjC,UAAU,EAAI,CAACxB,UAAU,eACxBV,IAAA,QACEkE,SAAS,CAAC,2BAA2B,CACrCM,OAAO,CAAEA,CAAA,GAAM,CACbrC,aAAa,CAAC,KAAK,CAAC,CACpBJ,eAAe,CAAC,IAAI,CAAC,CACvB,CAAE,CAAAoC,QAAA,cAEFjE,KAAA,QACEgE,SAAS,CAAC,kDAAkD,CAC5DM,OAAO,CAAGM,CAAC,EAAKA,CAAC,CAACC,eAAe,CAAC,CAAG;AAAA,CAAAZ,QAAA,eAErCnE,IAAA,OAAIkE,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAC,mBAAiB,CAAI,CAAC,cACnDnE,IAAA,MAAGkE,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAC,sEAAoE,CAAG,CAAC,cAE1GnE,IAAA,UACEgF,IAAI,CAAC,MAAM,CACXC,GAAG,CAAE7C,YAAa,CAClB8C,QAAQ,CAAEzC,gBAAiB,CAC3B0C,MAAM,CAAC,SAAS,CAChBf,KAAK,CAAE,CAAEgB,OAAO,CAAE,MAAO,CAAE,CAC5B,CAAC,cAEFlF,KAAA,QAAKgE,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7BnE,IAAA,WACEkE,SAAS,CAAC,qCAAqC,CAC/CM,OAAO,CAAEA,CAAA,QAAAa,qBAAA,QAAAA,qBAAA,CAAMjD,YAAY,CAACkD,OAAO,UAAAD,qBAAA,iBAApBA,qBAAA,CAAsBE,KAAK,CAAC,CAAC,EAAC,CAC7CC,QAAQ,CAAExD,SAAU,CACpBoC,KAAK,CAAE,CACLQ,eAAe,CAAEX,cAAc,CAAC5D,SAAS,CAACmB,MAAM,CAAC,CACjDqD,SAAS,aAAA5D,MAAA,CAAcgD,cAAc,CAAC5D,SAAS,CAACmB,MAAM,CAAC,oCACzD,CAAE,CAAA2C,QAAA,CAEDrC,YAAY,CAAG,cAAc,CAAG,cAAc,CACzC,CAAC,CAERA,YAAY,eACX5B,KAAA,QAAKgE,SAAS,CAAC,oBAAoB,CAAAC,QAAA,eACjCjE,KAAA,MAAAiE,QAAA,eAAGnE,IAAA,WAAQoE,KAAK,CAAE,CAAEqB,KAAK,CAAExB,cAAc,CAAC5D,SAAS,CAACmB,MAAM,CAAE,CAAE,CAAA2C,QAAA,CAAC,WAAS,CAAQ,CAAC,cAAAnE,IAAA,QAAK,CAAC,CAAC8B,YAAY,CAAC4D,IAAI,EAAI,CAAC,cAC9GxF,KAAA,QAAKgE,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7BnE,IAAA,WACEkE,SAAS,CAAC,sCAAsC,CAChDM,OAAO,CAAE1B,YAAa,CACtB0C,QAAQ,CAAExD,SAAU,CACpBoC,KAAK,CAAE,CACLQ,eAAe,CAAE,SAAS,CAC1BC,SAAS,CAAE,kDACb,CAAE,CAAAV,QAAA,CAEDnC,SAAS,CAAG,cAAc,CAAG,gBAAgB,CACxC,CAAC,cACThC,IAAA,WACEkE,SAAS,CAAC,qCAAqC,CAC/CM,OAAO,CAAEA,CAAA,GAAM,CACbrC,aAAa,CAAC,KAAK,CAAC,CACpBJ,eAAe,CAAC,IAAI,CAAC,CACvB,CAAE,CACFyD,QAAQ,CAAExD,SAAU,CACpBoC,KAAK,CAAE,CACLQ,eAAe,CAAE,SAAS,CAC1BC,SAAS,CAAE,kDACb,CAAE,CAAAV,QAAA,CACH,QAED,CAAQ,CAAC,EACN,CAAC,EACH,CACN,CAGA,CAACrC,YAAY,eACZ9B,IAAA,WACEkE,SAAS,CAAC,qCAAqC,CAC/CM,OAAO,CAAEA,CAAA,GAAM,CACbrC,aAAa,CAAC,KAAK,CAAC,CACpBJ,eAAe,CAAC,IAAI,CAAC,CACvB,CAAE,CACFyD,QAAQ,CAAExD,SAAU,CACpBoC,KAAK,CAAE,CACLQ,eAAe,CAAE,SAAS,CAC1BC,SAAS,CAAE,kDACb,CAAE,CAAAV,QAAA,CACH,QAED,CAAQ,CACT,EACE,CAAC,EACH,CAAC,CACH,CACN,CAEAvC,WAAW,eACV5B,IAAA,QACEkE,SAAS,CAAC,2BAA2B,CACrCM,OAAO,CAAEA,CAAA,GAAM3C,cAAc,CAAC,KAAK,CAAE,CAAAsC,QAAA,cAErCjE,KAAA,QACEgE,SAAS,CAAC,2BAA2B,CACrCM,OAAO,CAAGM,CAAC,EAAKA,CAAC,CAACC,eAAe,CAAC,CAAG;AAAA,CAAAZ,QAAA,eAErCjE,KAAA,MAAAiE,QAAA,eAAGnE,IAAA,WAAQoE,KAAK,CAAE,CAAEqB,KAAK,CAAExB,cAAc,CAAC5D,SAAS,CAACmB,MAAM,CAAE,CAAE,CAAA2C,QAAA,CAAC,OAAK,CAAQ,CAAC,cAAAnE,IAAA,QAAK,CAAC,CAACK,SAAS,CAACsE,IAAI,EAAI,CAAC,cACvGzE,KAAA,MAAAiE,QAAA,eAAGnE,IAAA,WAAQoE,KAAK,CAAE,CAAEqB,KAAK,CAAExB,cAAc,CAAC5D,SAAS,CAACmB,MAAM,CAAE,CAAE,CAAA2C,QAAA,CAAC,SAAO,CAAQ,CAAC,cAAAnE,IAAA,QAAK,CAAC,CAACK,SAAS,CAACsF,MAAM,EAAI,CAAC,cAC3GzF,KAAA,MAAAiE,QAAA,eAAGnE,IAAA,WAAQoE,KAAK,CAAE,CAAEqB,KAAK,CAAExB,cAAc,CAAC5D,SAAS,CAACmB,MAAM,CAAE,CAAE,CAAA2C,QAAA,CAAC,QAAM,CAAQ,CAAC,cAAAnE,IAAA,QAAK,CAAC,CAACgE,cAAc,CAAC3D,SAAS,CAACmB,MAAM,CAAC,EAAI,CAAC,cAC1HtB,KAAA,MAAAiE,QAAA,eAAGnE,IAAA,WAAQoE,KAAK,CAAE,CAAEqB,KAAK,CAAExB,cAAc,CAAC5D,SAAS,CAACmB,MAAM,CAAE,CAAE,CAAA2C,QAAA,CAAC,mBAAiB,CAAQ,CAAC,cAAAnE,IAAA,QAAK,CAAC,CAACK,SAAS,CAACuF,WAAW,EAAI,0BAA0B,EAAI,CAAC,cACxJ1F,KAAA,MAAAiE,QAAA,eAAGnE,IAAA,WAAQoE,KAAK,CAAE,CAAEqB,KAAK,CAAExB,cAAc,CAAC5D,SAAS,CAACmB,MAAM,CAAE,CAAE,CAAA2C,QAAA,CAAC,qBAAmB,CAAQ,CAAC,cAAAnE,IAAA,QAAK,CAAC,CAACK,SAAS,CAACwF,UAAU,EAAI,MAAM,EAAI,CAAC,EAClI,CAAC,CACH,CACN,EACE,CAAC,CACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAA1F,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
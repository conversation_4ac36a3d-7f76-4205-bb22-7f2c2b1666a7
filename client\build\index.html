<!doctype html><html lang="en"><head><meta charset="utf-8"/><link id="dynamic-favicon" rel="icon" href="" type="image/png"><meta name="viewport" content="width=device-width,initial-scale=1"/><meta name="theme-color" content="#000000"/><meta name="description" content="Bimbo Hunter - Official 2025 Bimbo Hunt Game"/><link rel="manifest" href="/manifest.json"/><title>Bimbo Hunter</title><script defer="defer" src="/static/js/main.fdf17fa7.js"></script><link href="/static/css/main.260f9dca.css" rel="stylesheet"></head><body><noscript>You need to enable JavaScript to run this app.</noscript><div id="root"></div><script>!function(){const n=["/thumbnails/Frieren.png","/thumbnails/Momo Ayase.png","/thumbnails/Biscuit Krueger.png","/thumbnails/<PERSON>nni the Witch.png","/thumbnails/<PERSON><PERSON>.png","/thumbnails/<PERSON>.png","/thumbnails/Anya Forger.png","/thumbnails/Sailor Mars.png","/thumbnails/Reze.png","/thumbnails/Nanakusa Nazuna.png"],a=n[Math.floor(Math.random()*n.length)],t=document.getElementById("dynamic-favicon");t&&(t.href=a)}()</script></body></html>